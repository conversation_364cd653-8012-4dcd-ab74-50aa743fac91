export default {

  standard: {
    sewage: {
      composite: {
        value: '综排一级',
        label: '评价标准：《污水综合排放标准》（GB8978-1996）一级标准',
        ph: {
          min: 4,
          max: 11,
          normalMin: 6,
          normalMax: 9
        },
        cod: {
          min: 0,
          max: 150,
          normal: 100
        },
        tN: {
          min: 0,
          max: 20,
          normal: 15
        },
        nH3N: {
          min: 0,
          max: 20,
          warn: 8,
          normal: 15
        },
        tP: {
          min: 0,
          max: 0.75,
          normal: 0.5
        },
        oil: {
          min: 0,
          max: 1.5,
          normal: 1
        },
        sS: {
          min: 0,
          max: 105,
          normal: 70
        }
      },
      town: {
        value: '城镇一级A',
        label: '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准',
        ph: {
          min: 4,
          max: 11,
          normalMin: 6,
          normalMax: 9
        },
        cod: {
          min: 0,
          max: 75,
          normal: 50
        },
        tN: {
          min: 0,
          max: 27,
          normal: 18
        },
        nH3N: {
          min: 0,
          max: 12,
          warn: 5,
          normal: 8
        },
        tP: {
          min: 0,
          max: 0.75,
          normal: 0.5
        },
        oil: {
          min: 0,
          max: 1.5,
          normal: 1
        },
        sS: {
          min: 0,
          max: 15,
          normal: 10
        }
      }
    }
  },

  oilGuobiaoA: {
    name: '石油类',
    maxValue: 1.0,
    minValue: 0
  },

  tNGuobiaoA: {
    name: '总氮',
    maxValue: 15,
    minValue: 0
  },

  wOilGuobiaoA: {
    name: '水中油',
    maxValue: 1.0,
    minValue: 0
  },

  codGuobiaoA: {
    name: 'COD',
    maxValue: 50,
    minValue: 0
  },

  codGuobiaoB: {
    name: 'COD',
    maxValue: 100,
    minValue: 0
  },

  pHGuobiaoA: {
    name: 'pH',
    maxValue: 9,
    minValue: 6
  },

  nH3NGuobiaoA: {
    name: '氨氮',
    maxValue: 8,
    minValue: 0
  },
  nH3NGuobiaoB: {
    name: '氨氮',
    maxValue: 15,
    minValue: 0
  },
  tPGuobiaoA: {
    name: '总磷',
    maxValue: 0.5,
    minValue: 0
  },
  sSGuobiaoA: {
    name: '浊度',
    anotherName: '浊度',
    maxValue: 10,
    minValue: 0
  },
  sSGuobiaoB: {
    name: '浊度',
    anotherName: '浊度',
    maxValue: 70,
    minValue: 0
  },

  nO2GuobiaoA: {
    name: '二氧化氮',
    maxValue: 0.08,
    minValue: 0
  },
  nOXGuobiaoA: {
    name: '氮氧化物',
    maxValue: 0.1,
    minValue: 0
  },
  cOGuobiaoA: {
    name: '一氧化碳',
    maxValue: 4,
    minValue: 0
  },
  pM10GuobiaoA: {
    name: 'PM10',
    maxValue: 0.15,
    minValue: 0
  },
  pM25GuobiaoA: {
    name: 'PM2.5',
    maxValue: 0.075,
    minValue: 0
  },
  sO2GuobiaoA: {
    name: '二氧化硫',
    maxValue: 0.150,
    minValue: 0
  },
  nOGuobiaoA: {
    name: '一氧化氮',
    maxValue: 0.100,
    minValue: 0
  },
  tSPGuobiaoA: {
    name: '总悬浮颗粒物',
    maxValue: 300,
    minValue: 0
  },

  noiseSDGuobiaoA: {
    name: 'SD',
    maxValue: 70,
    minValue: 0
  },
  noiseMinGuobiaoA: {
    name: 'min',
    maxValue: 70,
    minValue: 0
  },
  noiseMaxGuobiaoA: {
    name: 'max',
    maxValue: 70,
    minValue: 0
  },
  noiseL10GuobiaoA: {
    code: 'L10',
    name: '累计百分声级L10',
    maxValue: 70,
    minValue: 0
  },
  noiseLdnGuobiaoA: {
    code: 'Ldn',
    name: '昼夜等效声级',
    maxValue: 70,
    minValue: 0
  },
  noiseL50GuobiaoA: {
    name: 'L50',
    maxValue: 70,
    minValue: 0
  },
  noiseL5GuobiaoA: {
    name: 'L5',
    maxValue: 70,
    minValue: 0
  },
  noiseLdGuobiaoA: {
    name: 'Ld',
    maxValue: 70,
    minValue: 0
  },
  noisePRFGuobiaoA: {
    name: 'PRF',
    maxValue: 70,
    minValue: 0
  },
  noiseL95GuobiaoA: {
    name: 'L95',
    maxValue: 70,
    minValue: 0
  },
  noiseRateGuobiaoA: {
    name: 'Rate',
    maxValue: 70,
    minValue: 0
  },
  noiseL90GuobiaoA: {
    name: 'L90',
    maxValue: 70,
    minValue: 0
  },
  noiseLeqGuobiaoA: {
    name: 'Leq',
    normalMin: 10,
    normalMax: 100,
    maxValue: 70,
    minValue: 55
  },
  noiselafpGuobiaoA: {
    code: 'Lafp',
    name: 'Lafp',
    maxValue: 70,
    minValue: 0
  },
  noiseLnGuobiaoA: {
    code: 'Ln',
    name: '夜间等效声级',
    maxValue: 70,
    minValue: 0
  },

  SUCCESS: 0,
  ERROR: 1,

  classifications: [
    {
      label: '污水类',
      fLabel: '水质指标',
      value: 'sewage',
      factors: [
        {
          value: 'pH',
          label: 'pH'
        },
        {
          value: 'cod',
          label: 'COD'
        },
        {
          value: 'nH3N',
          label: '氨氮'
        },
        {
          value: 'tP',
          label: '总磷'
        },
        {
          value: 'oil',
          label: '水中油'
        },
        {
          value: 'oil',
          label: '石油类'
        },
        {
          value: 'sS',
          label: '浊度'
        },
        {
          value: 'flow',
          label: '流量'
        },
      ]
    },
    {
      label: '大气类',
      fLabel: '大气指标',
      value: 'air',
      factors: [
        {
          value: 'SO2',
          label: '二氧化硫'
        },
        {
          value: 'NO2',
          label: '二氧化氮'
        },
        {
          value: 'NO',
          label: '一氧化氮'
        },
        {
          value: 'CO',
          label: '一氧化碳'
        },
        {
          value: 'PM1.0',
          label: 'PM1.0'
        },
        {
          value: 'PM2.5',
          label: 'PM2.5'
        },
        {
          value: 'PM10',
          label: 'PM10'
        },
        {
          value: 'T',
          label: '温度'
        },
        {
          value: 'BP',
          label: '大气压'
        },
        {
          value: 'WD',
          label: '风向'
        },
        {
          value: 'WS',
          label: '风速'
        },
        {
          value: 'H',
          label: '湿度'
        },
        {
          value: 'TSP',
          label: 'TSP'
        },
      ]
    },
    {
      label: '噪声类',
      fLabel: '噪声指标',
      value: 'noise',
      factors: [
        {
          value: 'intensity',
          label: '噪声强度'
        },
        {
          value: 'Leq',
          label: 'Leq'
        },
        {
          value: 'frequency',
          label: '噪声频率'
        },
      ]
    },
    {
      label: '固废类',
      value: 'solid',
      fLabel: '固废指标',
      factors: [
        {
          value: 'capacity',
          label: '垃圾压缩池池容'
        }
      ]
    }
  ],
  //  '类型:1.污水,2.大气,3.噪声,4.固废',
  classifiConfig: [
    {
      label: '污水',
      value: 1
    },
    {
      label: '大气',
      value: 2
    },
    {
      label: '噪声',
      value: 3
    },
    {
      label: '固废',
      value: 4
    }
  ],

  cities: [
    {
      name: "武汉",
      counties: ["江岸", "江汉", "硚口", "汉阳", "武昌", "青山", "洪山", "东西湖",
        "汉南", "蔡甸", "江夏", "黄陂", "新洲"]
    },
    {
      name: "黄石",
      counties: ["黄石港", "西塞山", "下陆", "铁山", "阳新", "大冶"]
    },
    {
      name: "十堰",
      counties: ["茅箭", "张湾", "郧阳", "郧西", "竹山", "竹溪", "房", "丹江口"]
    },
    {
      name: "宜昌",
      counties: ["西陵", "伍家岗", "点军", "猇亭", "夷陵", "远安", "兴山", "秭归",
        "长阳", "五峰", "宜都", "当阳", "枝江"]
    },
    {
      name: "襄阳",
      counties: ["襄城", "樊城", "襄州", "南漳", "谷城", "保康", "老河口", "枣阳",
        "宜城"]
    },
    {
      name: "鄂州",
      counties: ["梁子湖", "华容", "鄂城"]
    },
    {
      name: "荆门",
      counties: ["东宝", "掇刀", "京山", "沙洋", "钟祥"]
    },
    {
      name: "孝感",
      counties: ["孝南", "孝昌", "大悟", "云梦", "应城", "安陆", "汉川"]
    },
    {
      name: "荆州",
      counties: ["沙市", "荆州", "公安", "监利", "江陵", "石首", "洪湖", "松滋"]
    },
    {
      name: "黄冈",
      counties: ["黄州", "团风", "红安", "罗田", "英山", "浠水", "蕲春", "黄梅", "麻城", "武穴"]
    },
    {
      name: "咸宁",
      counties: ["咸安", "嘉鱼", "通城", "崇阳", "通山", "赤壁"]
    },
    {
      name: "随州",
      counties: ["曾都", "随县", "广水"]
    },
    {
      name: "恩施",
      counties: ["恩施", "利川", "建始", "巴东", "宣恩", "咸丰", "来凤", "鹤峰"]
    },
    {
      name: "直辖县级",
      counties: ["仙桃", "潜江", "天门", "神农架林"]
    }
  ],

  defaultPermissionGroup: [
    {
      path: '/map',
      name: 'map',
      label: '地图监控',
      checked: true,
    },
    {
      path: '/screen',
      name: 'screen',
      label: '大屏展示',
      checked: true,
    },
    {
      path: '/realTime',
      name: 'realTime',
      label: '实时数据',
      checked: true,
    },
    {
      path: '/history',
      name: 'history',
      label: '历史数据',
      checked: true,
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      label: '数据面板',
      checked: true,
    },
    {
      path: '/workflow',
      name: 'workflow',
      label: '流程控制',
      checked: true,
    },
    {
      path: '/processing',
      name: 'processing',
      label: '指标报警',
      checked: true,
      permission: [{ id: 'processing:edit', value: 'edit', label: '编辑', checked: true },
      { id: 'processing:export', value: 'export', label: '导出', checked: true }]
    },
    {
      path: '/configuration',
      name: 'configuration',
      label: '报警配置',
      checked: true,
      permission: [{ id: 'configuration:remove', value: 'remove', label: '删除', checked: true },
      { id: 'configuration:add', value: 'add', label: '新增', checked: true },
      { id: 'configuration:edit', value: 'edit', label: '编辑', checked: true }]
    },
    {
      path: '/daily',
      name: 'daily',
      label: '日报',
      checked: true,
      permission: [{ id: 'daily:export', value: 'export', label: '导出', checked: true }]
    },
    {
      path: '/monthly',
      name: 'monthly',
      label: '月报',
      checked: true,
      permission: [{ id: 'monthly:export', value: 'export', label: '导出', checked: true }]
    },
    {
      path: '/user',
      name: 'user',
      label: '用户管理',
      checked: false,
      permission: [{ id: 'user:remove', value: 'remove', label: '删除', checked: false },
      { id: 'user:add', value: 'add', label: '新增', checked: false },
      { id: 'user:edit', value: 'edit', label: '编辑', checked: false }]
    },
    {
      path: '/role',
      name: 'role',
      label: '角色权限管理',
      checked: false,
      permission: [{ id: 'role:remove', value: 'remove', label: '删除', checked: false },
      { id: 'role:add', value: 'add', label: '新增', checked: false },
      { id: 'role:edit', value: 'edit', label: '编辑', checked: false },
      { id: 'role:grant', value: 'grant', label: '分配权限', checked: false }]
    },
    {
      path: '/logger',
      name: 'logger',
      label: '日志管理',
      checked: false,
    }
  ]

}
