export default {
  /**
   * 根据权限渲染home侧边菜单
   * @param context
   */
  renderHomeMenu(context) {
    let routes = context.$router.options.routes;
    if (context.currentUser.roleVo && context.currentUser.roleVo.permissionGroup) {
      for (let firstRoute of routes) {
        if (firstRoute.children) {
          for (let secondRoute of firstRoute.children) {
            for (let privilege of context.currentUser.roleVo.permissionGroup) {
              if (secondRoute.name === privilege.name) {
                secondRoute.menuShow = true;
                firstRoute.menuShow = true;
              }
            }
          }
        }
      }
    }
  },

  /**
   * check用户是否登录
   * @param home
   */
  userLoginCheck(home) {
    home.$router.beforeEach((to, from, next) => {
      // 如果是登录页面则不需要判断权限
      if (to.fullPath !== '/login' || to.fullPath !== '/error/401') {
        if (!home.currentUser) {
          next({path: "/login", replace: true})
        }
      } else {
        next();
      }
    })
  },
  /**
   * 权限导航钩子
   * @param home home组件
   */
  navHook(home) {
    setDataPermission(home.$router, home.currentUser.roleVo.permissionGroup);
    home.$router.beforeEach((to, from, next) => {
      // 如果是登录页面则不需要判断权限
      if (to.fullPath !== '/login' || to.fullPath !== '/error/401') {
        console.log('当前用户权限列表：', home.currentUser.roleVo.permissionGroup);
        let isPermission = false;
        home.currentUser.roleVo.permissionGroup.forEach((permission) => {
          if (permission.path === to.fullPath) {
            isPermission = true
          }
        });
        if (!isPermission) {
          /**
           * 需要闵一设计用户没有访问权限的界面
           */
          next({path: "/error/401", replace: true})
        } else {
          next();
        }
      } else {
        next();
      }
    })
  }
}

/**
 * 设置数据级权限，将权限permission数据插入到路由表中
 * @param router 路由
 * @param permissionGroup 权限组数据
 */
function setDataPermission(router, permissionGroup) {
  for (let element of permissionGroup) {
    let routeItem = router.match(element.path);
    if (routeItem) {
      // 将返回的所有数据都存到路由的meta信息中
      routeItem.meta.permission = element.permission;
    }
  }
}




