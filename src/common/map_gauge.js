import constants from './constants'

function setSeriesName(factorName) {
  let name = '';
  constants.classifications[0].factors.forEach(factor => {
    if (factor.label === factorName) {
      name = constants.classifications[0].fLabel;
    }
  });
  constants.classifications[1].factors.forEach(factor => {
    if (factor.label === factorName) {
      name = constants.classifications[1].fLabel;
    }
  });
  constants.classifications[2].factors.forEach(factor => {
    if (factor.label === factorName) {
      name = constants.classifications[2].fLabel;
    }
  });
  return name;
}

export default {
  setSeriesName: setSeriesName
}

