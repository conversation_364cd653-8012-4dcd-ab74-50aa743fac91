import CONSTANTS from '../common/constants';

async function dynamicRefreshAir(context) {
  let latest = await context.getAirFactorLatestValueAct({
    airId: context.area.airId, systemCode: context.sewageSystem.systemCode,
    factorCode: context.factor.code
  });
  let xAxisData = context.airChartInfo.xAxis.data;
  if (latest.time !== xAxisData[xAxisData.length - 1]) {
    pushAirElement(context, xAxisData, latest)
  }
}

function pushAirElement(context, xAxisData, latest) {
  xAxisData.push(latest.time);
  context.airChartInfo.series[0].data.push(latest.value);
  context.airChartInfo.series[1].data.push(context.factor.maxValue);
  // if (context.factor.name === CONSTANTS.nO2GuobiaoA.name) {
  //   context.airChartInfo.series[1].data.push(CONSTANTS.nO2GuobiaoA.maxValue);
  // } else if (context.factor.name === CONSTANTS.nOXGuobiaoA.name) {
  //   context.airChartInfo.series[1].data.push(CONSTANTS.nOXGuobiaoA.maxValue);
  // } else if (context.factor.name === CONSTANTS.cOGuobiaoA.name) {
  //   context.airChartInfo.series[1].data.push(CONSTANTS.cOGuobiaoA.maxValue);
  // } else if (context.factor.name === CONSTANTS.pM10GuobiaoA.name) {
  //   context.airChartInfo.series[1].data.push(CONSTANTS.pM10GuobiaoA.maxValue);
  // } else if (context.factor.name === CONSTANTS.pM25GuobiaoA.name) {
  //   context.airChartInfo.series[1].data.push(CONSTANTS.pM25GuobiaoA.maxValue);
  // } else if (context.factor.name === CONSTANTS.sO2GuobiaoA.name) {
  //   context.airChartInfo.series[1].data.push(CONSTANTS.sO2GuobiaoA.maxValue);
  // } else if (context.factor.name === CONSTANTS.nOGuobiaoA.name) {
  //   context.airChartInfo.series[1].data.push(CONSTANTS.nOGuobiaoA.maxValue);
  // } else if (context.factor.name === CONSTANTS.tSPGuobiaoA.name) {
  //   context.airChartInfo.series[1].data.push(CONSTANTS.tSPGuobiaoA.maxValue);
  // }
  context.chartLine.setOption(context.airChartInfo);
}

async function dynamicRefreshSewage(context) {
  let latest = await context.getFactorLatestValueAct({
    sewageId: context.area.sewageId, systemCode: context.sewageSystem.systemCode,
    factorCode: context.factor.code
  });
  let xAxisData = context.sewageChartInfo.xAxis.data;
  if (latest.time !== xAxisData[xAxisData.length - 1]) {
    pushSewageElement(context, xAxisData, latest)
  }
}

function pushSewageElement(context, xAxisData, latest) {
  xAxisData.push(latest.time);
  context.sewageChartInfo.series[0].data.push(latest.value);

  if (context.factor.name === CONSTANTS.pHGuobiaoA.name) {
    context.sewageChartInfo.series[1].data.push(CONSTANTS.pHGuobiaoA.minValue);
    context.sewageChartInfo.series[2].data.push(CONSTANTS.pHGuobiaoA.maxValue);
  } else if (context.factor.name === CONSTANTS.codGuobiaoA.name) {
    context.sewageChartInfo.series[1].data.push(CONSTANTS.codGuobiaoA.maxValue);
  } else if (context.factor.name === CONSTANTS.nH3NGuobiaoA.name) {
    context.sewageChartInfo.series[1].data.push(CONSTANTS.nH3NGuobiaoA.maxValue);
  } else if (context.factor.name === CONSTANTS.oilGuobiaoA.name) {
    context.sewageChartInfo.series[1].data.push(CONSTANTS.oilGuobiaoA.maxValue);
  } else if (context.factor.name === CONSTANTS.sSGuobiaoA.name) {
    context.sewageChartInfo.series[1].data.push(CONSTANTS.sSGuobiaoA.maxValue);
  } else if (context.factor.name === CONSTANTS.tNGuobiaoA.name) {
    context.sewageChartInfo.series[1].data.push(CONSTANTS.tNGuobiaoA.maxValue);
  } else if (context.factor.name === CONSTANTS.tPGuobiaoA.name) {
    context.sewageChartInfo.series[1].data.push(CONSTANTS.tPGuobiaoA.maxValue);
  }
  context.chartLine.setOption(context.sewageChartInfo);
}

async function dynamicRefreshNoise(context) {
  let latest = await context.getNoiseFactorLatestValueAct({
    noiseId: context.area.noiseId, systemCode: context.sewageSystem.systemCode,
    factorCode: context.factor.code
  });
  let xAxisData = context.noiseChartInfo.xAxis.data;
  if (latest.time !== xAxisData[xAxisData.length - 1]) {
    pushNoiseElement(context, xAxisData, latest)
  }
}

function pushNoiseElement(context, xAxisData, latest) {
  xAxisData.push(latest.time);
  context.noiseChartInfo.series[0].data.push(latest.value);
  // 因为交投的监测设置只有leq，暂时只判断该监测factor
  if (context.factor.name === CONSTANTS.noiseLeqGuobiaoA.name) {
    context.noiseChartInfo.series[1].data.push(CONSTANTS.noiseLeqGuobiaoA.maxValue);
  }
  context.chartLine.setOption(context.noiseChartInfo);
}

function airChartInfo(context) {
  let guobiaoSeries = {
    name: '上限',
    type: 'line',
    lineStyle: {
      color: '#C1272D'
    },
    itemStyle: {
      color: '#C1272D'
    },
    data: {},
    smooth: true,
    showAllSymbol: true,
  };
  let guobiaoSeriesMax = {
    name: '',
    type: 'line',
    lineStyle: {
      color: '#C1272D'
    },
    itemStyle: {
      color: '#C1272D'
    },
    data: {},
    smooth: true,
    showAllSymbol: true,
  };
  let series = [
    {
      name: context.factor.name,
      type: 'line',
      lineStyle: {
        color: '#006837'
      },
      itemStyle: {
        color: '#006837'
      },
      data: context.factorValues.map(item => item.value),
      smooth: true,
      showAllSymbol: true,
    },
    guobiaoSeriesMax
  ];
  // if (context.factor.maxValue) {
  //   guobiaoSeriesMax.name = "上限";
  //   guobiaoSeriesMax.data = context.factorValues.map(item => context.factor.maxValue);
  // }
  if (context.factor.code === 'SO2') {
    guobiaoSeriesMax.name = "上限";
    guobiaoSeriesMax.data = context.factorValues.map(item => CONSTANTS.sO2GuobiaoA.maxValue);
  } else if (context.factor.code === 'NO2') {
    guobiaoSeriesMax.name = '上限';
    guobiaoSeriesMax.data = context.factorValues.map(item => CONSTANTS.nO2GuobiaoA.maxValue);
  } else if (context.factor.code === 'NO') {
    guobiaoSeriesMax.name = '上限';
    guobiaoSeriesMax.data = context.factorValues.map(item => CONSTANTS.nOGuobiaoA.maxValue);
  } else if (context.factor.code === 'TSP') {
    guobiaoSeriesMax.name = '上限';
    guobiaoSeriesMax.data = context.factorValues.map(item => CONSTANTS.tSPGuobiaoA.maxValue);
  } else if (context.factor.code === 'PM2.5') {
    guobiaoSeriesMax.name = '上限';
    guobiaoSeriesMax.data = context.factorValues.map(item => CONSTANTS.pM25GuobiaoA.maxValue);
  } else if (context.factor.code === 'PM10.0') {
    guobiaoSeriesMax.name = '上限';
    guobiaoSeriesMax.data = context.factorValues.map(item => CONSTANTS.pM10GuobiaoA.maxValue);
  } else if (context.factor.code === 'CO') {
    guobiaoSeriesMax.name = '上限';
    guobiaoSeriesMax.data = context.factorValues.map(item => CONSTANTS.cOGuobiaoA.maxValue);
  } else if (context.factor.code === 'NOx') {
    guobiaoSeriesMax.name = '上限';
    guobiaoSeriesMax.data = context.factorValues.map(item => CONSTANTS.nOXGuobiaoA.maxValue);
  }
  return {
    title: {
      x: 'center',
      text: '监测指标：' + context.factor.name,
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      x: 'left',
      y: 'top',
      data: [context.factor.name, guobiaoSeries.name, guobiaoSeriesMax.name]
    },
    dataZoom: {
      show: true,
      realtime: true,
      start: 0,
      end: 100
    },
    toolbox: {
      show: true,
      feature: {
        mark: {show: true},
        dataView: {show: true, readOnly: false},
        magicType: {show: true, type: ['line', 'bar']},
        restore: {show: true},
        saveAsImage: {show: true}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: context.factorValues.map(item => item.time),
    },
    yAxis: {
      name: buildUnit(context.factor.unit),
      type: 'value',
      splitLine: {
        show: false
      }
    },
    series: series
  }
}

function buildUnit(unit) {
  if (unit !== null) {
    return ' 单位(' + unit + ')';
  }
  return '';
}

function noiseChartInfo(context) {
  let guobiaoSeries = {
    name: '',
    type: 'line',
    data: {},
    smooth: true,
    lineStyle: {
      color: '#C1272D'
    },
    itemStyle: {
      color: '#C1272D'
    },
    showAllSymbol: true,
  };
  let guobiaoSeriesMax = {
    name: '',
    type: 'line',
    data: {},
    smooth: true,
    lineStyle: {
      color: '#C1272D'
    },
    itemStyle: {
      color: '#C1272D'
    },
    showAllSymbol: true,
  };
  let series = [
    {
      name: context.factor.name,
      type: 'line',
      lineStyle: {
        color: '#006837'
      },
      itemStyle: {
        color: '#006837'
      },
      data: context.factorValues.map(item => item.value),
      smooth: true,
      showAllSymbol: true,
    },
    guobiaoSeries
  ];
  if (context.factor.code === 'Leq') {
    guobiaoSeriesMax.name = "昼间";
    guobiaoSeries.name = "夜间";
    guobiaoSeries.data = context.factorValues.map(item => CONSTANTS.noiseLeqGuobiaoA.minValue);
    guobiaoSeriesMax.data = context.factorValues.map(item => CONSTANTS.noiseLeqGuobiaoA.maxValue);
    series = [{
      name: context.factor.name,
      type: 'line',
      lineStyle: {
        color: '#006837'
      },
      itemStyle: {
        color: '#006837'
      },
      data: context.factorValues.map(item => item.value),
      smooth: true,
      showAllSymbol: true,
    }, guobiaoSeries, guobiaoSeriesMax];
  }
  return {
    title: {
      x: 'center',
      text: '监测指标：' + context.factor.name,
      //subtext: '数据来源 - ' + this.currentSystem.name,
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      x: 'left',
      y: 'top',
      data: [context.factor.name, guobiaoSeries.name, guobiaoSeriesMax.name]
    },
    dataZoom: {
      show: true,
      realtime: true,
      start: 0,
      end: 100
    },
    toolbox: {
      show: true,
      feature: {
        mark: {show: true},
        dataView: {show: true, readOnly: false},
        magicType: {show: true, type: ['line', 'bar']},
        restore: {show: true},
        saveAsImage: {show: true}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      // axisLine: {onZero: false},
      data: context.factorValues.map(item => item.time),
    },
    yAxis: {
      name: ' 单位(' + context.factor.unit + ')' || '',
      type: 'value',
      // max: factor.maxValue,
      splitLine: {
        show: false
      }
    },
    series: series
  }
}

function commonChartInfo(context) {
  let guobiaoSeries = {
    name: '',
    type: 'line',
    data: {},
    smooth: true,
    lineStyle: {
      color: '#C1272D'
    },
    itemStyle: {
      color: '#C1272D'
    },
    showAllSymbol: true,
  };
  let guobiaoSeriesMax = {
    name: '',
    type: 'line',
    data: {},
    smooth: true,
    lineStyle: {
      color: '#C1272D'
    },
    itemStyle: {
      color: '#C1272D'
    },
    showAllSymbol: true,
  };
  let series = [
    {
      name: context.factor.name,
      type: 'line',
      lineStyle: {
        color: '#006837'
      },
      itemStyle: {
        color: '#006837'
      },
      data: context.factorValues.map(item => item.value),
      smooth: true,
      showAllSymbol: true,
    },
    guobiaoSeries
  ];
  return {
    title: {
      x: 'center',
      text: '监测指标：' + context.factor.name,
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      x: 'left',
      data: [context.factor.name, guobiaoSeries.name, guobiaoSeriesMax.name]
    },
    dataZoom: {
      show: true,
      realtime: true,
      start: 0,
      end: 100
    },
    toolbox: {
      show: true,
      feature: {
        mark: {show: true},
        dataView: {show: true, readOnly: false},
        magicType: {show: true, type: ['line', 'bar']},
        restore: {show: true},
        saveAsImage: {show: true}
      }
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      // axisLine: {onZero: false},
      data: context.factorValues.map(item => item.time),
    },
    yAxis: {
      name: buildUnit(context.factor.unit),
      type: 'value',
      // max: factor.maxValue,
      splitLine: {
        show: false
      }
    },
    series: series
  }
}

export default {
  dynamicRefreshSewage: dynamicRefreshSewage,
  dynamicRefreshAir: dynamicRefreshAir,
  dynamicRefreshNoise: dynamicRefreshNoise,
  airChartInfo: airChartInfo,
  noiseChartInfo: noiseChartInfo,
  commonChartInfo: commonChartInfo
}
