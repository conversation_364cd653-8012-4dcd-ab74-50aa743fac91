import API from "../api/api_sewage";
function contains(holder, str) {
  return holder.indexOf(str) !== -1;
}

/**
 * 从指定数组中通过指定属性删除对应元素
 * @param list 待遍历的数组
 * @param entity
 * @param attr
 */
function removeElement(list, entity, attr) {
  for (let index = 0; index <= list.length; index++) {
    if (entity[attr] === list[index][attr]) {
      list.splice(index, 1);
      break;
    }
  }
}

function groupArray(list, attr) {
  let map = {},
    dest = [];
  for (let i = 0; i < list.length; i++) {
    let ai = list[i];
    if (!map[ai[attr]]) {
      dest.push({
        initial: ai[attr],
        list: [ai]
      });
      map[ai[attr]] = ai;
    } else {
      for (let j = 0; j < dest.length; j++) {
        let dj = dest[j];
        if (dj.initial === ai[attr]) {
          dj.list.push(ai);
          break;
        }
      }
    }
  }
  return dest;
}

function deepCopy(obj) {
  if (typeof obj !== 'object') {
    return obj;
  }
  let newobj = {};
  for (let attr in obj) {
    if (attr !== null) {
      newobj[attr] = deepCopy(obj[attr]);
    }
  }
  return newobj;
}

/**
 * 设置权限组中checked属性
 * @param permissionGroup 权限组list
 * @param value true or false
 */
function setPermissionGroupChecked(permissionGroup, value) {
  for (let menuPermission of permissionGroup) {
    menuPermission.checked = value;
    if (contains(Object.getOwnPropertyNames(menuPermission), 'permission')) {
      for (let dataPermission of menuPermission.permission) {
        dataPermission.checked = value
      }
    }
  }
}

function resetForm(formName, context) {
  context.$refs[formName].resetFields();
}

/**
 * 获取一个随机数字符串（如必要自行转Number），通过指定范围和小数位
 * @param {Number} min 最小值
 * @param {Number} max 最大值
 * @param {Number} decimalPlace 小数位个数 example: 2 两个小数位
 * @return {String}
 * <AUTHOR>
 */
function getRandomNumberByRange(min, max, decimalPlace) {
  if ((!min && min !== 0) || (!max && max !== 0)) {
    return "";
  }
  if (decimalPlace > 0) {
    return (Math.random() * (max - min) + min).toFixed(decimalPlace) + "";
  } else {
    return Math.floor(Math.random() * (max - min) + min) + "";
  }
}

/**
 * 水质、大气、噪声和固废四类的system和factors的加载（已完成水质、固废）
 * @param context
 * @param area
 * @returns {Promise<any>}
 */
function setSystemAndFactors(context, area) {
  console.log("setSystemAndFactors",context, area);
  return new Promise(async (resolve, reject) => {
    try {
      let sewageSys = {};
      let solidSys = {};
      let airSys = {};
      let noiseSys = {};
      if (area.sewageId) {  //污水类
        sewageSys = await sewageSysAndFactors(context, area);
      } else {
        context.setSewageSystemAct({});
      }
      if (area.noiseId) {  //噪声类
        noiseSys = await noiseSysAndFactors(context, area);
      } else {
        context.setNoiseSystemAct({});
      }
      if (area.solidId) {  //固废类
        solidSys = await solidSysAndFactors(context, area);
      } else {
        context.setSolidSystemAct({});
      }
      if (area.airId) {   //大气类
        airSys = await airSysAndFactors(context, area);
      } else {
        context.setAirSystemAct({});
      }
      resolve({sewageSys: sewageSys, solidSys: solidSys, airSys: airSys, noiseSys: noiseSys});
    } catch (err) {
      reject(err);
    }
  })
}

/**
 * 用于同一服务区内tab切换
 * @param context
 * @param area
 * @param curType
 * @returns {Promise<any>}
 */
function setPollutionSystemAndFactors(context, area, curType) {
  return new Promise(async (resolve, reject) => {
    try {
      let sewageSys = {};
      let solidSys = {};
      let airSys = {};
      let noiseSys = {};
      if (curType === 'noise') {
        noiseSys = await noiseSysAndFactors(context, area);
        context.setSewageSystemAct({});
        context.setAirSystemAct({});
        context.setSolidSystemAct({});
      } else if (curType === 'solid') {
        solidSys = await solidSysAndFactors(context, area);
        context.setSewageSystemAct({});
        context.setAirSystemAct({});
        context.setNoiseSystemAct({});
      } else if (curType === 'air') {
        airSys = await airSysAndFactors(context, area);
        context.setSewageSystemAct({});
        context.setSolidSystemAct({});
        context.setNoiseSystemAct({});
      } else if (curType === 'sewage') {
        sewageSys = await sewageSysAndFactors(context, area);
        context.setSolidSystemAct({});
        context.setNoiseSystemAct({});
        context.setAirSystemAct({});
      }
      resolve({sewageSys: sewageSys, solidSys: solidSys, airSys: airSys, noiseSys: noiseSys})
    } catch (e) {
      reject(e);
    }
  })
}

/**
 *
 * @param context
 * @param area
 * @returns {Promise<any>}
 */
function setArea(context, area) {
  return new Promise(async (resolve, reject) => {
    try {
      context.setCurrentArea(area);
      resolve();
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 噪声污染源system和factors的加载
 * @param context
 * @param area
 */
function noiseSysAndFactors(context, area) {
  return new Promise(async (resolve, reject) => {
    try {
      let systems = await context.getNoiseSystemsAct(area);
      let factPageVo = await context.getNoiseFactorsAct({
        noiseId: area.noiseId,
        systemCode: systems[0].systemCode
      });
      systems[0].factors = factPageVo.factorVos;
      context.setNoiseSystemAct(systems[0]);
      resolve(systems[0]);
    } catch (e) {
      reject(e);
    }
  })
}

/**
 * 固废污染源system和factors的加载
 * @param context
 * @param area
 */
function solidSysAndFactors(context, area) {
  return new Promise(async (resolve, reject) => {
    try {
      let systems = await context.getSolidSystemsAct(area);
      let factPageVo = await context.getSolidFactorsAct({
        solidId: area.solidId,
        systemCode: systems[0].systemCode
      });
      systems[0].factors = factPageVo.factorVos;
      context.setSolidSystemAct(systems[0]);
      resolve(systems[0]);
    } catch (e) {
      reject(e)
    }
  })
}

/**
 * 水质污染源system和factors的加载
 * @param context
 * @param area
 * @returns {Promise<*>}
 */
function sewageSysAndFactors(context, area) {
  return new Promise(async (resolve, reject) => {
    try {
      let systems = await context.getSewageSystemsAct(area);
      let factPageVo = await context.getSystemFactorsAct({
        sewageId: area.sewageId,
        systemCode: systems[0].systemCode
      });
      // 设置当前系统因子
      systems[0].factors = factPageVo.factorVos;
      context.setSewageSystemAct(systems[0]);
      resolve(systems[0]);
    } catch (e) {
      reject(e)
    }
  })
}

function airSysAndFactors(context, area) {
  return new Promise(async (resolve, reject) => {
    try {
      let systems = await context.getAirSystemsAct(area);
      let factPageVo = await context.getAirSystemFactorsAct({
        airId: area.airId,
        systemCode: systems[0].systemCode
      });
      // 设置当前系统因子
      systems[0].factors = factPageVo.factorVos;
      context.setAirSystemAct(systems[0]);
      resolve(systems[0]);
    } catch (e) {
      reject(e)
    }
  })
}

function isStrEmpty(str) {
  if ((typeof str === 'undefined') || (str === null) || (str.length === 0)) {
    return true;
  } else {
    return (false);
  }
}

/**
 *污水数据个数异常处理
 * @param params
 * @returns {Promise<unknown>}
 */
function firstNotEmpty(params) {
  return new Promise(async (resolve, reject) => {
    try {
      if (params.factors) {
        for (let index in params.factors) {
          let factor = params.factors[index];
          let factorParams = {
            sewageId: params.sewageId,
            systemCode: params.systemCode,
            factorCode: factor.code,
            dateStart: params.dateStart,
            dateEnd: params.dateEnd,
            page: params.pagination.page,
            limit: params.pagination.limit
          };
          let factorHistories = await API.getFactorHistoryValues(factorParams);
          if (factorHistories.factorValueVos.length !== 0) {
            resolve(factorHistories);
          }
        }
      }
    } catch (err) {
      reject(err)
    }
  })
}

function dealResultData(res) {
  if (res && res.data && res.data.errcode === 0) {
    return res.data.data
  } else if (res && res.data && res.data.errcode !== 0) {
    alert(res.data.errmsg);
    console.error(res.data.errmsg);
    return null
  } else {
    alert("系统错误，请刷新页面");
    console.error("系统错误，请刷新页面");
    return null
  }
}

/**
 * 数组去重
 * @param array
 */
function arrayUnique(array) {
  let newArray = [array[0]];
  array.forEach(item => {
    if (!newArray.includes(item)) {
      newArray.push(item);
    }
  });
  return newArray;
}
function setMonitorType(area) {
  if (area.sewageId) {
    return '污水类';
  } else if (area.airId) {
    return '大气类';
  } else if (area.noiseId) {
    return '噪声类';
  } else {
    return '固废类'
  }
}

/**
 * 读取文件刘转化为fileStr
 * @param rawFile
 * @returns {Promise<any>}
 */
function loadImg(rawFile) {
  return new Promise(function (resolve, reject) {
    let reader = new FileReader();
    //读取文件过程方法
    reader.onloadstart = function (e) {
      console.log("开始读取....");
    };
    reader.onprogress = function (e) {
      console.log("正在读取中....");
    };
    reader.onabort = function (e) {
      console.log("中断读取....");
      reject();
    };
    reader.onerror = function (e) {
      console.log("读取异常....");
      reject();
    };
    reader.onload = function (e) {
      console.log("成功读取....");
      resolve(e.target.result);
    };
    reader.readAsDataURL(rawFile)
  });
}

export default {
  contains: contains,
  removeElement: removeElement,
  copyObject: deepCopy,
  setPermissionGroupChecked: setPermissionGroupChecked,
  elemResetForm: resetForm,
  groupArray: groupArray,
  getRandomNumberByRange,
  setSystemAndFactors: setSystemAndFactors,
  isStrEmpty: isStrEmpty,
  setPollutionSystemAndFactors: setPollutionSystemAndFactors,
  firstNotEmpty: firstNotEmpty,
  arrayUnique: arrayUnique,
  dealResultData: dealResultData,
  setMonitorType: setMonitorType,
  loadImg: loadImg
}
