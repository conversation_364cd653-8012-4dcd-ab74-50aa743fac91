let SIGN_REGEXP = /([yMdhsm])(\1*)/g;
let DEFAULT_PATTERN = 'yyyy-MM-dd';
let MONTH_PATTERN = 'yyyy-MM';
let YYYYMMDDHHMMSS = 'yyyy-MM-dd hh:mm:ss';

export default {
  getPreDateYYYYMMDD: getPreDateYYYYMMDD,
  getDateYYYYMMDD: getDateYYYYMMDD,
  dateFormat: format,
  dateParse: parse,
  getDateNextDateYYYYMMDD: getDateNextDateYYYYMMDD,
  YYYYMMDDHHMMSS: YYYYMMDDHHMMSS
}

function padding(s, len) {
  let l = len - (s + '').length;
  for (let i = 0; i < l; i++) {
    s = '0' + s;
  }
  return s;
}

function getDateYYYYMMDD() {
  let curDate = new Date();
  let stringDate = new Date(curDate.getTime());
  return format(stringDate, DEFAULT_PATTERN)
}

function getPreMonth() {
  let curDate = new Date();
  let stringDate = new Date(curDate.getTime() - 30 * 24 * 60 * 60 * 1000);
  return format(stringDate, MONTH_PATTERN)
}

function getPreDateYYYYMMDD() {
  let curDate = new Date();
  let stringDate = new Date(curDate.getTime() - 24 * 60 * 60 * 1000);
  return format(stringDate, DEFAULT_PATTERN)
}

function getDateNextDateYYYYMMDD(date) {
  //let curDate = this.dateParse(date, DEFAULT_PATTERN);
  let stringDate = new Date(date.getTime() + 24 * 60 * 60 * 1000);
  return format(stringDate, DEFAULT_PATTERN);
}

function format(date, pattern) {
  pattern = pattern || DEFAULT_PATTERN;
  return pattern.replace(SIGN_REGEXP, function ($0) {
    switch ($0.charAt(0)) {
      case 'y':
        return padding(date.getFullYear(), $0.length);
      case 'M':
        return padding(date.getMonth() + 1, $0.length);
      case 'd':
        return padding(date.getDate(), $0.length);
      case 'w':
        return date.getDay() + 1;
      case 'h':
        return padding(date.getHours(), $0.length);
      case 'm':
        return padding(date.getMinutes(), $0.length);
      case 's':
        return padding(date.getSeconds(), $0.length)
    }
  })
}

function parse(dateString, pattern) {
  let matchs1 = pattern.match(SIGN_REGEXP);
  let matchs2 = dateString.match(/(\d)+/g);
  if (matchs1.length === matchs2.length) {
    let _date = new Date(1970, 0, 1);
    for (let i = 0; i < matchs1.length; i++) {
      let _int = parseInt(matchs2[i]);
      let sign = matchs1[i];
      switch (sign.charAt(0)) {
        case 'y':
          _date.setFullYear(_int);
          break;
        case 'M':
          _date.setMonth(_int - 1);
          break;
        case 'd':
          _date.setDate(_int);
          break;
        case 'h':
          _date.setHours(_int);
          break;
        case 'm':
          _date.setMinutes(_int);
          break;
        case 's':
          _date.setSeconds(_int);
          break;
      }
    }
    return _date
  }
  return null
}
