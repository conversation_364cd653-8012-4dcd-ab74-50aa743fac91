/**
 * 服务区污水、大气等分类的仪表盘对话框
 * @param context mapContext
 * @param area
 */
import {MessageBox} from 'element-ui';

function showGaugeDialog(context, area) {
  let index = context.userServiceAreas.findIndex(item => item.code === area.code);
  if (index === -1) {
    MessageBox.alert("对当前服务区没有管理权限！", "警告");
    return;
  }
  // 暂时由setCurrentArea方法来替代
  context.setCurrentArea(area);
  context.setCurrentAreaName(area.name);
  context.$router.push({path: '/realTimeData'});
  // context.setAreaByClick(area).then(system => {
  //   context.$router.push({path: '/realTimeData'});
  // })
}

function goControlPage(context, area) {
  let index = context.userControlAreas.findIndex(item=> item.mac === area.mac);
  if (index === -1) {
    MessageBox.alert("对当前控制点没有管理权限！", "警告");
    return;
  }
  // context.setCurrentGatewayAct({});
  context.gateways.forEach(gateway => {
    if (gateway.mac === area.mac) {
      console.log(gateway);
      context.setChosenGatewayAct(gateway);
    }
  })
  context.$router.push({path: '/workflow'})
}

function showAreaInfoWindow(context, area) {
  context.mouseHoverForArea(area);
}

/**
 * 临时使用，针对不同服务区弹出框中显示对应的tab
 * @param context
 * @param area
 */
function showPollutionTabTemp(context, area) {
  context.visible.sewageTab = true;
  context.visible.gasTab = true;
  context.visible.noiseTab = true;
  context.visible.solidTab = false;
  context.activeTab = 'sewage';

  if (area.name === '孝感服务区（京珠高速）') {
    context.visible.sewageTab = true;
  } else if (area.name === '汉十孝感服务区') {
    context.activeTab = 'wasteGas';
    context.visible.gasTab = true;
    context.visible.noiseTab = true;
  } else if (area.name === '麻竹大悟段芳畈收费站') {
    context.visible.sewageTab = true;
  } else if (area.name === '麻竹大悟段装八寨服务区') {
    context.visible.sewageTab = true;
  } else if (area.name === '麻竹大悟段夏店收费站') {
    context.visible.sewageTab = true;
  } else if (area.name === '圈环孝感南段垌冢收费站') {
    context.visible.sewageTab = true;
  } else if (area.name === '武汉西收费站') {
    context.activeTab = 'wasteGas';
    context.visible.gasTab = true;
    context.visible.noiseTab = true;
  } else if (area.name === '麻竹大悟段河口收费站') {
    context.visible.sewageTab = true;
  } else if (area.name === '京珠东西湖服务区') {
    context.visible.sewageTab = true;
  } else {
    context.visible.sewageTab = true;
    context.visible.gasTab = true;
    context.visible.noiseTab = true;
  }
}

/**
 * 把临时数组的中服务区加到正常服务数组，并删除无监测服务区
 * @param tempAreas
 * @param areaNoMo
 * @param map
 * @param context
 * @param normalMarkers
 */
function setAreaToGreen(tempAreas, areaNoMo, map, context, normalMarkers) {
  tempAreasName.forEach(areaName => {
    // for (let index in areasNoMo) {
    //   let areaNoMo = areasNoMo[index];
    if (areaNoMo.name === areaName) {
      //areasNoMo.splice(index, 1);
      let marker = new BMap.Marker(new BMap.Point(areaNoMo.lon, areaNoMo.lat));
      marker.setTitle(areaNoMo.name);
      // 允许覆盖物在map.clearOverlays方法中被清除
      marker.enableMassClear();
      marker.setIcon(new BMap.Icon('../../static/icon/area_green.png', new BMap.Size(32, 32)));
      normalMarkers.push(marker);
      map.addOverlay(marker);// 将标注添加到地图中
      marker.addEventListener('click', function (event) {
        showGaugeDialog(context, area);
      });
      return true;
      //   break;
      // }
    }
  });
  return false;
}

let tempAreasName = ["武汉西收费站", "汉十孝感服务区",
  "麻竹大悟段装八寨服务区", "麻竹大悟段夏店收费站",
  "沉湖收费站", "福星收费站", "中洲收费站", "应城北收费站", "辛榨收费站", "垌冢收费站（新河口）"];

export default {

  initAreaPreLogo(context, map, areas, areasNoMo, activeControls, inactiveControls) {
    console.log(activeControls);
    let normalMarkers = [];
    let alarmMarkers = [];
    let grayMarkers = [];
    let activeMarkers = [];
    let inactiveMarkers = [];

    areas.forEach((area) => {
      let marker = new BMap.Marker(new BMap.Point(area.lon, area.lat));
      marker.setTitle(area.name);
      // 允许覆盖物在map.clearOverlays方法中被清除
      marker.enableMassClear();
      if (area.state === false) {
        marker.setIcon(new BMap.Icon('../../static/icon/area_green.png', new BMap.Size(32, 32)));
        normalMarkers.push(marker);
      } else {
        marker.setIcon(new BMap.Icon('../../static/icon/area_red.png', new BMap.Size(32, 32)));
        alarmMarkers.push(marker);
      }
      map.addOverlay(marker);// 将标注添加到地图中
      marker.addEventListener('click', function (event) {
        showGaugeDialog(context, area);
      });
      // 取消鼠标悬浮显示对话框的事件
      // marker.addEventListener('mouseover', function (event) {
      //   setTimeout(function () {
      //     showAreaInfoWindow(context, area)
      //   }, 1*1000);
      //showAreaInfoWindow(context, area);
      // })
    });

    areasNoMo.forEach(areaNoMo => {
      let grayMarker = new BMap.Marker(new BMap.Point(areaNoMo.lon, areaNoMo.lat));
      grayMarker.setIcon(new BMap.Icon('../../static/icon/area_gray.png', new BMap.Size(32, 32)));
      grayMarker.setTitle(areaNoMo.name);
      grayMarkers.push(grayMarker);
      map.addOverlay(grayMarker);
      // grayMarker.addEventListener('mouseover', function (event) {
      //   showAreaInfoWindow(context, areaNoMo);
      // });
    });

    activeControls.forEach(control => {
      let activeMarker = new BMap.Marker(new BMap.Point(control.longitude, control.latitude));
      activeMarker.setIcon(new BMap.Icon('../../static/icon/control_green.png', new BMap.Size(32, 32)));
      activeMarker.setTitle(control.name);
      activeMarkers.push(activeMarker);
      map.addOverlay(activeMarker);
      activeMarker.addEventListener('click', function (event) {
        goControlPage(context, control);
      });
    });

    inactiveControls.forEach(control => {
      let inactiveMarker = new BMap.Marker(new BMap.Point(control.longitude, control.latitude));
      inactiveMarker.setIcon(new BMap.Icon('../../static/icon/control_gray.png', new BMap.Size(32, 32)));
      inactiveMarker.setTitle(control.name);
      inactiveMarkers.push(inactiveMarker);
      map.addOverlay(inactiveMarker);
    })
    return {normal: normalMarkers, alarm: alarmMarkers, gray: grayMarkers, active: activeMarkers, inactive: inactiveMarkers};
  },

  initControlLogo(context, map, activeControls, inactiveControls) {

  },
  /**
   * 初始化服务区的logo 标识
   * @param context mapContext
   * @param map
   * @param areas 有监测服务区
   * @param areasNoMo 无监测服务区
   * @returns {{normal: Array, alarm: Array, gray: Array}}
   */
  initAreaLogo(context, map, areas, areasNoMo) {
    let normalMarkers = [];
    let alarmMarkers = [];
    let grayMarkers = [];
    areas.forEach((area) => {
      // let marker = new BMap.Marker(new BMap.Point(area.lon, area.lat));

      // marker.setTitle(area.name);
      // 允许覆盖物在map.clearOverlays方法中被清除
      // marker.enableMassClear();

      if (area.state === false) {
        let marker = new BMapLib.RichMarker('<div style="text-align: center;\n' +
          '    border: 1px solid #1A483E;\n' +
          '    border-radius: 5px;\n' +
          '    background: #FFFFFF;\n' +
          '    height: 1.9em;\n' +
          '    line-height: 1.9em;\n' +
          '    width: 12em;">\n' +
          '      <span style="font-size: .9rem; margin-left: .5em; ">' + area.name + '</span>\n' +
          '      <img src="../../../static/icon/area_green.png"\n' +
          '           style="width:2em;height:2em;padding:0px;margin:0px;vertical-align:middle;"/>\n' +
          '    </div>', new BMap.Point(area.lon, area.lat), new BMap.Size(32, 32));
        // marker.setIcon(new BMap.Icon('../../static/icon/area_green.png', new BMap.Size(32, 32)));
        normalMarkers.push(marker);
        map.addOverlay(marker);// 将标注添加到地图中
        marker.addEventListener('click', function (event) {
          showGaugeDialog(context);
        })
      } else {
        let marker = new BMapLib.RichMarker('<div style="text-align: center;\n' +
          '    border: 1px solid #1A483E;\n' +
          '    border-radius: 5px;\n' +
          '    background: #FFFFFF;\n' +
          '    height: 1.9em;\n' +
          '    line-height: 1.9em;\n' +
          '    width: 12em;">\n' +
          '      <span style="font-size: .9rem; margin-left: .5em; ">' + area.name + '</span>\n' +
          '      <img src="../../../static/icon/area_red.png"\n' +
          '           style="width:2em;height:2em;padding:0px;margin:0px;vertical-align:middle;"/>\n' +
          '    </div>', new BMap.Point(area.lon, area.lat), new BMap.Size(32, 32));
        // marker.setIcon(new BMap.Icon('../../static/icon/area_red.png', new BMap.Size(32, 32)));
        alarmMarkers.push(marker);
        map.addOverlay(marker);// 将标注添加到地图中
        marker.addEventListener('click', function (event) {
          showGaugeDialog(context);
        })
      }
      // map.addOverlay(marker);// 将标注添加到地图中
      // marker.addEventListener('click', function (event) {
      //   showGaugeDialog(context);
      // })
    });
    areasNoMo.forEach(areasNoMo => {
      let grayMarker = new BMapLib.RichMarker('<div style="text-align: center;\n' +
        '    border: 1px solid #1A483E;\n' +
        '    border-radius: 5px;\n' +
        '    background: #FFFFFF;\n' +
        '    height: 1.9em;\n' +
        '    line-height: 1.9em;\n' +
        '    width: 10em;">\n' +
        '      <span style="font-size: .9rem; margin-left: .5em; ">' + areasNoMo.name + '</span>\n' +
        '      <img src="../../../static/icon/area_gray.png"\n' +
        '           style="width:2em;height:2em;padding:0px;margin:0px;vertical-align:middle;"/>\n' +
        '    </div>', new BMap.Point(areasNoMo.lon, areasNoMo.lat), new BMap.Size(32, 32));
      map.addOverlay(grayMarker);
      grayMarkers.push(grayMarker);
      // let grayMarker = new BMap.Marker(new BMap.Point(areasNoMo.lon, areasNoMo.lat));
      // grayMarker.setIcon(new BMap.Icon('../../static/icon/area_gray.png', new BMap.Size(32, 32)));
      // grayMarker.setTitle(areasNoMo.name);
      // map.addOverlay(grayMarker)
    });
    return {normal: normalMarkers, alarm: alarmMarkers, gray: grayMarkers};
  },
  /**
   * 在地图上绘制行政区域边界
   * @param cityName
   * @param baiduMap
   */
  drawBoundary(cityName, baiduMap) {
    let bdary = new BMap.Boundary();
    bdary.get(cityName, function (rs) {       //获取行政区域
      let count = rs.boundaries.length; //行政区域的点有多少个
      for (let i = 0; i < count; i++) {
        let ply = new BMap.Polyline(rs.boundaries[i], {
          strokeWeight: 5,
          strokeColor: "#FF0000",
          fillOpacity: 0.01
          //fillColor: "#FFFFFF"
        }); //建立多边形覆盖物
        baiduMap.addOverlay(ply);  //添加覆盖物
        // baiduMap.setViewport(ply.getPath())//调整视野
      }
    });
  },

  /**
   * 生成服务区聚合图，暂时只将未有监测点的服务区聚合
   * @param areasObj {{normal: Array, alarm: Array, gray: Array}}
   */
  generateMarkerClusterer(areasObj, baiduMap) {
    let markerClusterer = new BMapLib.MarkerClusterer(baiduMap, {markers: areasObj.gray});
    return {markerClusterer: markerClusterer, markers: areasObj.gray};
  },

  stationInfoWindows(area, marker) {
    let pollution = '';
    let designStandard = '';
    let eidStandard = '';
    if (area.stationRelationInfoVo.pollutions.length > 0) {
      pollution = area.stationRelationInfoVo.pollutions[0];
      if (area.stationRelationInfoVo.pollutions.length > 1) {
        for (let i = 1; i < area.stationRelationInfoVo.pollutions.length; i++) {
          pollution += '\n';
          pollution += area.stationRelationInfoVo.pollutions[i];
        }
      }
    }
    if (area.stationRelationInfoVo.designStands.length > 0) {
      designStandard = area.stationRelationInfoVo.designStands[0];
      if (area.stationRelationInfoVo.designStands.length > 1) {
        for (let i = 1; i < area.stationRelationInfoVo.designStands.length; i++) {
          designStandard += '\n';
          designStandard += area.stationRelationInfoVo.designStands[i];
        }
      }
    }
    if (area.stationRelationInfoVo.eiaStands.length > 0) {
      eidStandard = area.stationRelationInfoVo.eiaStands[0];
      if (area.stationRelationInfoVo.eiaStands.length > 1) {
        for (let i = 1; i < area.stationRelationInfoVo.eiaStands.length; i++) {
          eidStandard += '\n';
          eidStandard += area.stationRelationInfoVo.eiaStands[i];
        }
      }
    }
    let html = '<div class="card bg-light mb-3" style="width: 38rem; height: 16rem">';
    html += '    <div class="container">';
    html += '      <div class="row">';
    html += '        <div class="col-6">';
    // html += '          <div>';
    // html += '            <div >污染源名称：' + area.name + '</div>';
    // html += '          </div>';
    html += '         <div style="width: 20rem; height: 6rem; padding-left: 0.5rem; padding-top: 0.5rem">';
    html += '          <div class="row"><div class="card-text col-4" style="padding-left: 0;">污染源名称：</div><div class="col-6" style="padding: 0;">';
    html += '            <div >' + area.name + '</div>';
    html += '          </div></div>';
    html += '          <div class="row"><div class="card-text col-4" style="padding-left: 0;">污染源类别：</div><div class="col-6" style="padding: 0;">';
    for (let i = 0; i < area.stationRelationInfoVo.pollutions.length; i++) {
      html += '<div style="padding-left: 0">' + area.stationRelationInfoVo.pollutions[i] + '</div>';
    }
    html += '          </div></div>';
    html += '          <div class="row"><div class="card-text col-4" style="padding-left: 0;">设计标准：</div><div class="col-6" style="padding: 0;">';
    for (let i = 0; i < area.stationRelationInfoVo.designStands.length; i++) {
      html += '<div style="padding-left: 0">' + area.stationRelationInfoVo.designStands[i] + '</div>';
    }
    html += '          </div></div>';
    html += '          <div class="row"><div class="card-text col-4" style="padding-left: 0;">环评标准：</div><div class="col-6" style="padding: 0;">';
    for (let i = 0; i < area.stationRelationInfoVo.eiaStands.length; i++) {
      html += '<div style="padding-left: 0; margin: 0; text-align: left">' + area.stationRelationInfoVo.eiaStands[i] + '</div>';
    }
    html += '          </div></div>';
    html += '         </div>';
    html += '       </div>';
    html += '       <div class="col-6" style="padding-top: 1rem">';
    html += '          <div class="" style="overflow-x:auto;white-space:nowrap;height: 12rem;width:16rem;">';
    html += '                <img style="height: 100%;width: 100%" src=' + area.stationRelationInfoVo.pic + '>';
    html += '          </div>';
    html += '       </div>';
    html += '      </div>';
    html += '    </div>';
    html += '  </div>';
    let infoWindow = new BMap.InfoWindow(html);
    marker.openInfoWindow(infoWindow);
  },


  defaultStation(area, marker, imgUrl, standard) {
    let infoWindow = new BMap.InfoWindow('<div class="card bg-light mb-3" style="width: 32rem; height: 16rem">'
      + '    <div class="container">'
      + '      <div class="row">'
      + '        <div class="col-6">'
      + '          <div style="margin: 0.5rem">'
      + '            <p class="card-text">污染源名称：</p>'
      + '          </div>'
      + '          <div style="margin: 0.5rem 1rem">'
      + '            <p class="card-text">' + area.name + '</p>'
      + '          </div>'
      + '        <div style="width: 16rem; height: 6rem">'
      + '            <div style="margin: 0.5rem">'
      + '              <p class="card-text">污染源类别：</p>'
      + '            </div>'
      + '            <div style="margin: 0.5rem 1rem">'
      + '              <p class="card-text">水环境污染源</p>'
      + '          </div>'
      + '          <div><p class="card-text">' + standard + '</p></div>'
      + '        </div>'
      + '        </div>'
      + '        <div class="col-6">'
      + '          <div><p class="card-text">监测点位：</p></div>'
      + '          <div class="" style="overflow-x:auto;white-space:nowrap;height: 10.5rem;width:100%;">'
      + '            <div>'
      + '              <div style="height:9rem;">'
      + '                <img style="height:8rem;" src=' + imgUrl + '>'
      + '                <p class="card-text">污水监测点位</p>'
      + '              </div>'
      + '            </div>'
      + '          </div>'
      + '        </div>'
      + '      </div>'
      + '    </div>'
      + '  </div>');
    marker.openInfoWindow(infoWindow);
  },

  airNoiseStation(area, marker, airImgUrl, noiseImgUrl) {
    let infoWindow = new BMap.InfoWindow('<div class="card bg-light mb-3" style="width: 32rem">'
      + '    <div class="container">'
      + '      <div class="row">'
      + '        <div class="col-6">'
      + '          <div style="margin: 0.5rem">'
      + '            <p class="card-text">污染源名称：</p>'
      + '          </div>'
      + '          <div style="margin: 0.5rem 1rem">'
      + '            <p class="card-text">' + area.name + '</p>'
      + '          </div>'
      + '        <div style="width: 16rem; height: 6rem">'
      + '            <div style="margin: 0.5rem">'
      + '              <p class="card-text">污染源类别：</p>'
      + '            </div>'
      + '            <div style="margin: 0.5rem 1rem">'
      + '              <p class="card-text">空气环境污染源、噪声环境污染源</p>'
      + '          </div>'
      + '          <div><p class="card-text">评价标准：《环境空气质量标准》' +
      '（GB3095-2012）二类标准；《声环境质量标准》（GB3096-2008）2类标准</p></div>'
      + '        </div>'
      + '        </div>'
      + '        <div class="col-6">'
      + '           <div style="margin: 0.5rem"><p class="card-text">监测点位：</p></div>'
      + '          <div class="" style="overflow-x:auto;white-space:nowrap;margin: 0.5rem;width:100%;">'
      + '            <div>'
      + '              <div style=width:14rem;">'
      + '                <img style="height:8rem;width:14rem" src=' + airImgUrl + '>'
      + '                <p class="card-text">空气监测点位</p>'
      + '              </div>'
      + '              <div style="width:14rem;">'
      + '                <img style="height:8rem;width:14rem" src=' + noiseImgUrl + '>'
      + '                <p class="card-text">噪声监测点位</p>'
      + '              </div>'
      + '            </div>'
      + '          </div>'
      + '        </div>'
      + '      </div>'
      + '    </div>'
      + '  </div>');
    marker.openInfoWindow(infoWindow);
  },

  jingzhuXiaoganStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/jingzhuxiaogan_sewage.jpg",
      '评价标准：《污水综合排放标准》（GB8978-1996）一级标准')
  },


  hanshiStation(area, marker) {
    this.airNoiseStation(area, marker, "../../static/image/hanshi_air.jpg",
      "../../static/image/hanshi_noise.jpg")
  },

  wuhanxiStation(area, marker) {
    this.airNoiseStation(area, marker, "../../static/image/wuhanxi_air.jpg",
      "../../static/image/wuhanxi_noise.jpg")
  },

  hekouStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/hekou_sewage.jpg",
      '评价标准：《污水综合排放标准》（GB8978-1996）一级标准')
  },

  dongzhongStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/dongzhong_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准');
  },

  chenhuStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/dongzhong_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准');
  },

  fuxingStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/dongzhong_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准')
  },

  zhongzhouStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/dongzhong_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准');
  },

  xinhekouStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/dongzhong_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准')
  },

  yingchengbeiStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/dongzhong_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准')
  },

  xinzaStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/dongzhong_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准');
  },

  zhuangbazaiStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/zhuangbazai_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准');
  },

  fangfanStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/fangfan_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准');
  },

  xiadianStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/xiadian_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准');
  },

  wudangshanStation(area, marker) {
    this.defaultStation(area, marker, "../../static/image/wudangshan_sewage.jpg",
      '评价标准：《城镇污水处理厂污染物排放标准》（GB18918-2002）一级A标准');
  }

}
