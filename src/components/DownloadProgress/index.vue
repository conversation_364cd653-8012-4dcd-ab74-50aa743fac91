<template>
  <div v-if="visible" class="floating-progress-container">
    <div class="progress-card" :class="{'download-complete': progressPercentage === 100}">
      <div class="progress-header">
        <span class="progress-title">{{ progressStage }}</span>
        <i class="el-icon-close close-icon" @click="handleCancel" v-if="canCancel"></i>
      </div>
      <div class="progress-body">
        <!-- 当进度为0%时显示loading动画 -->
        <div v-if="progressPercentage === 0" class="loading-spinner-container">
          <div class="loading-spinner">
            <div class="spinner"></div>
          </div>
        </div>
        
        <!-- 有进度时显示正常进度条 -->
        <el-progress 
          v-else
          type="circle" 
          :percentage="Number(progressPercentage)" 
          :status="progressStatus"
          :stroke-width="8"
          :width="280"
          class="progress-circle">
        </el-progress>
        
        <div class="progress-info">
          <div class="download-text">{{ downloadInfo }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DownloadProgress',
  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 进度百分比
    progressPercentage: {
      type: Number,
      default: 0
    },
    // 进度状态
    progressStatus: {
      type: String,
      default: ''
    },
    // 进度阶段描述
    progressStage: {
      type: String,
      default: '正在准备下载...'
    },
    // 下载信息
    downloadInfo: {
      type: String,
      default: '请耐心等待...'
    },
    // 是否可以取消
    canCancel: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleCancel() {
      this.$confirm('确定要取消下载吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('cancel');
        this.$message.info('已取消下载');
      }).catch(() => {});
    }
  }
}
</script>

<style scoped>
/* 悬浮进度窗口样式 */
.floating-progress-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
}

.progress-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  width: 650px;
  padding: 50px;
  animation: slideIn 0.3s ease-out;
  transition: all 0.5s ease;
}

.download-complete {
  box-shadow: 0 0 25px rgba(103, 194, 58, 0.6);
  transform: scale(1.05);
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.progress-title {
  font-size: 22px;
  font-weight: bold;
  color: #303133;
}

.close-icon {
  cursor: pointer;
  color: #909399;
  transition: color 0.3s;
  font-size: 22px;
}

.close-icon:hover {
  color: #f56c6c;
}

.progress-body {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-info {
  margin-top: 25px;
  color: #606266;
  font-size: 16px;
  text-align: center;
  min-height: 50px;
  line-height: 1.5;
}

.download-text {
  white-space: pre-line;
  word-break: break-all;
  max-width: 100%;
}

/* 调整进度圆环大小 */
:deep(.el-progress-circle) {
  width: 280px !important;
  height: 280px !important;
}

/* 调整进度圆环文字大小 */
:deep(.el-progress__text) {
  font-size: 18px !important;
  font-weight: bold;
}

/* Loading动画容器 */
.loading-spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 280px;
  height: 280px;
}

/* Loading动画 */
.loading-spinner {
  position: relative;
  width: 120px;
  height: 120px;
}

.spinner {
  width: 100%;
  height: 100%;
  border: 8px solid #f3f3f3;
  border-top: 8px solid #409EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}
</style> 