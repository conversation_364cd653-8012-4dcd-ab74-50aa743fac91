<template>
  <div :class="{ 'hidden': hidden }" class="pagination-container">
    <el-pagination
      :background="background"
      :current-page="page"
      :page-size="limit"
      :layout="layout"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { scrollTo } from '@/utils/scroll-to'
import { ref, watch } from "vue";

const props = defineProps({
  total: {
    required: true,
    type: Number
  },
  page: {
    type: Number,
    default: 1
  },
  limit: {
    type: Number,
    default: 20
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 50]
    }
  },
  // 移动端页码按钮的数量端默认值5
  pagerCount: {
    type: Number,
    default: document.body.clientWidth < 992 ? 5 : 7
  },
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  background: {
    type: Boolean,
    default: true
  },
  autoScroll: {
    type: Boolean,
    default: true
  },
  hidden: {
    type: <PERSON>olean,
    default: false
  }
})

const emit = defineEmits(['update:page', 'update:limit', 'pagination']);

// 处理每页显示条数变化
function handleSizeChange(val) {
  // 如果当前页码乘以每页条数大于总数，则重置为第一页
  const newPage = (props.page * val > props.total) ? 1 : props.page;
  
  // 同时更新页码和每页条数
  emit('update:limit', val);
  
  // 如果页码需要重置，也更新页码
  if (newPage !== props.page) {
    emit('update:page', newPage);
  }
  
  // 触发分页事件 (只触发一次)
  emit('pagination', { page: newPage, limit: val });
}

// 处理页码变化
function handleCurrentChange(val) {
  // 只在页码真正变化时触发事件
  if (val !== props.page) {
    emit('update:page', val);
    emit('pagination', { page: val, limit: props.limit });
  }
}
</script>

<style scoped>
.pagination-container {
  background: #fff;
  padding: 32px 16px;
}
.pagination-container.hidden {
  display: none;
}
</style>