<template>
  <div ref="chartDom" :style="{ width: width, height: height }"></div>
</template>

<script setup>
import {
  ref,
  onMounted,
  onUnmounted,
  nextTick,
  defineExpose,
  markRaw,
} from "vue";
import * as echarts from "echarts";
const props = defineProps({
  option: {
    type: Object,
    default: () => {
      return {};
    },
  },
  width: {
    type: String,
    default: "auto",
  },
  height: {
    type: String,
    default: "100%",
  },
});
const emit = defineEmits(["handelClick"]);
// 创建一个响应式引用来保存DOM元素
const chartDom = ref(null);
let chartInstance = ref(null);
const initChart = async (option) => {
  try {
    await nextTick(); // 确保DOM已经渲染完成
    chartInstance.value = markRaw(echarts?.init(chartDom.value));
    chartInstance.value.showLoading();
    chartInstance.value.setOption(option);
    chartInstance.value.hideLoading();
    // chartInstance.value.on("click", async (params) => {
    //   // console.log("在这里处理点击事件", params);
    //   emit("handelClick", params);
    // });
  } catch (err) {
    console.log(err);
  } finally {
    chartInstance.value.hideLoading();
  }
};
const updataMap = (option) => {
  // console.log('跟新数据', option);
  chartInstance.value.setOption(option);
};
const resizeChart = () => {
  chartInstance.value.resize({
    width: "auto", // 宽度随着父容器变化而变化
    height: "auto", // 高度随着父容器变化而变化
  });
};
defineExpose({ updataMap });
// 初始化ECharts实例并设置配置项（这里以折线图为例，但可灵活替换）
onMounted(async () => {
  await initChart(props.option);
  await window.addEventListener("resize", () => {
    chartInstance.value.resize();
  });
});

// 销毁ECharts实例
onUnmounted(() => {
  if (chartInstance.value != null && chartInstance.value.dispose) {
    window.removeEventListener("resize", initChart);
    chartInstance?.value?.dispose();
  }
});
</script>

<style scoped>
/* 添加一些CSS样式来美化图表容器（可选） */
</style>
