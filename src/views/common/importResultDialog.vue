<template>
  <el-dialog 
    :title="title"
    v-model="visible" 
    width="80%" 
    append-to-body
    :close-on-click-modal="false"
  >
    <div v-if="importResult">
      <!-- 结果摘要 -->
      <el-alert
        :title="importResult.summary"
        :type="importResult.hasFailure ? 'warning' : 'success'"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />
      
      <!-- 详细结果 -->
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 成功列表 -->
        <el-tab-pane v-if="importResult.successNum > 0" :label="`成功记录 (${importResult.successNum})`" name="success">
          <div class="result-container">
            <div v-for="(msg, index) in importResult.successMsgList" :key="index" class="result-item success-item">
              <el-icon class="success-icon"><SuccessFilled /></el-icon>
              <span>{{ msg }}</span>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 失败列表 -->
        <el-tab-pane v-if="importResult.failureNum > 0" :label="`失败记录 (${importResult.failureNum})`" name="failure">
          <div class="result-container">
            <div v-for="(msg, index) in importResult.failureMsgList" :key="index" class="result-item failure-item">
              <el-icon class="failure-icon"><CircleCloseFilled /></el-icon>
              <span>{{ msg }}</span>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 统计信息 -->
        <el-tab-pane label="统计信息" name="statistics">
          <el-descriptions title="导入统计" :column="2" border>
            <el-descriptions-item label="总记录数">{{ importResult.total }}</el-descriptions-item>
            <el-descriptions-item label="成功记录数">
              <el-tag type="success">{{ importResult.successNum }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="新增记录数">
              <el-tag type="primary">{{ importResult.insertNum }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="更新记录数">
              <el-tag type="info">{{ importResult.updateNum }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="失败记录数">
              <el-tag type="danger">{{ importResult.failureNum }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="成功率">
              <el-tag :type="successRate >= 100 ? 'success' : successRate >= 80 ? 'warning' : 'danger'">
                {{ successRate }}%
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleConfirm">确定</el-button>
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '导入结果'
  },
  importResult: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'confirm', 'close'])

// 响应式数据
const visible = ref(false)
const activeTab = ref('statistics')

// 计算属性
const successRate = computed(() => {
  if (!props.importResult || props.importResult.total === 0) return 0
  return Math.round((props.importResult.successNum / props.importResult.total) * 100)
})

// 监听外部传入的visible状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.importResult) {
    // 根据结果自动选择显示的tab
    if (props.importResult.failureNum > 0) {
      activeTab.value = 'failure'
    } else if (props.importResult.successNum > 0) {
      activeTab.value = 'success'
    } else {
      activeTab.value = 'statistics'
    }
  }
})

// 监听内部visible状态变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 方法
const handleConfirm = () => {
  emit('confirm')
  handleClose()
}

const handleClose = () => {
  visible.value = false
  emit('close')
}
</script>

<style scoped>
.result-container {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  border-left: 4px solid;
}

.success-item {
  background-color: #f0f9ff;
  border-left-color: #67c23a;
  color: #67c23a;
}

.failure-item {
  background-color: #fef0f0;
  border-left-color: #f56c6c;
  color: #f56c6c;
}

.success-icon, .failure-icon {
  margin-right: 8px;
  font-size: 16px;
}

.success-icon {
  color: #67c23a;
}

.failure-icon {
  color: #f56c6c;
}

.result-item span {
  color: #606266;
}
</style> 