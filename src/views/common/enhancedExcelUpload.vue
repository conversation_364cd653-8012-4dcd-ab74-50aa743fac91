<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :destroy-on-close="true" @closed="closeDialog">
    <div class="enhanced-upload-container">
      <!-- 路线选择 -->
      <el-form label-width="120px" style="margin-bottom: 20px;">
        <el-form-item label="选择路线：" required>
          <el-select 
            v-model="roadId" 
            placeholder="请选择路线" 
            style="width: 100%"
            @change="handleRoadChange"
            :disabled="props.selectedRoadId !== null"
          >
            <el-option
              v-for="road in roadList"
              :key="road.id"
              :label="`${road.roadCode} - ${road.roadName}`"
              :value="road.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="路面技术状况：" required>
          <el-select 
            v-model="selectedConditionType" 
            placeholder="请选择路面技术状况" 
            style="width: 100%"
            @change="handleConditionChange"
            :disabled="props.selectedStatus !== ''"
          >
            <el-option
              v-for="option in conditionOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 模板下载区域 -->
      <div class="template-download">
        <el-button 
          type="primary" 
          icon="Download" 
          @click="downloadTemplate"
          :disabled="!canDownloadTemplate"
        >
          下载导入模板
        </el-button>
        <div class="upload-tip">
          请先选择路面技术状况类型，然后下载对应的Excel模板
        </div>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :data="uploadData"
          :file-list="fileList"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :on-remove="handleRemove"
          :auto-upload="false"
          :limit="1"
          accept=".xlsx,.xls"
          drag
          class="upload-demo"
        >
          <template #trigger>
            <el-button type="primary">选择文件</el-button>
          </template>
          
          <template #default>
            <el-icon class="el-icon--upload"><Upload /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="upload-tip">
              只能上传xlsx/xls文件，且不超过10MB
            </div>
          </template>
        </el-upload>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirmUpload" 
          :disabled="!canUpload"
          :loading="uploading"
        >
          {{ uploading ? '上传中...' : '确定上传' }}
        </el-button>
      </div>
    </template>

    <!-- 上传结果详情对话框 -->
    <el-dialog 
      v-model="resultDialogVisible" 
      title="导入结果详情" 
      width="80%" 
      :destroy-on-close="true"
      append-to-body
    >
      <div v-if="uploadResult">
        <!-- 汇总信息 -->
        <el-alert 
          :title="getResultSummary()" 
          :type="getResultType()" 
          show-icon 
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <!-- 统计信息 -->
        <el-descriptions :column="4" border style="margin-bottom: 20px;">
          <el-descriptions-item label="总计数据">{{ getTotalCount() }}</el-descriptions-item>
          <el-descriptions-item label="成功导入">{{ getSuccessCount() }}</el-descriptions-item>
          <el-descriptions-item label="失败数据">{{ getFailCount() }}</el-descriptions-item>
          <el-descriptions-item label="错误详情">{{ getErrorCount() }} 个</el-descriptions-item>
        </el-descriptions>

        <!-- 详细信息显示 -->
        <div v-if="getDetailMessage()" style="margin-bottom: 20px;">
          <h4 style="margin-bottom: 10px;">详细信息：</h4>
          <el-alert 
            :title="getDetailMessage()" 
            type="info" 
            :closable="false"
            show-icon
          />
        </div>

        <!-- 错误详情表格 -->
        <div v-if="hasErrors()">
          <h4 style="margin-bottom: 10px;">错误详情：</h4>
          <el-table 
            :data="getErrorList()" 
            border 
            stripe 
            max-height="400"
            style="width: 100%"
          >
            <el-table-column prop="sheet" label="工作表" width="100" v-if="hasColumn('sheet')" />
            <el-table-column prop="row" label="行号" width="80" v-if="hasColumn('row')" />
            <el-table-column prop="field" label="字段" width="120" v-if="hasColumn('field')" />
            <el-table-column prop="message" label="错误信息" min-width="200" />
            <el-table-column prop="cellValue" label="单元格值" width="120" v-if="hasColumn('cellValue')" />
            <el-table-column prop="errorMsg" label="错误原因" min-width="200" v-if="hasColumn('errorMsg')" />
          </el-table>
        </div>
        
        <!-- 成功导入的情况 -->
        <div v-else-if="isSuccessful()">
          <el-result
            icon="success"
            title="导入成功"
            :sub-title="`成功导入 ${getSuccessCount()} 条数据`"
          />
        </div>

        <!-- 调试信息（开发环境显示） -->
        <div v-if="showDebugInfo()" style="margin-top: 20px;">
          <el-collapse>
            <el-collapse-item title="调试信息（开发环境）" name="debug">
              <pre>{{ JSON.stringify(uploadResult, null, 2) }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="resultDialogVisible = false">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { listRoad } from '@/api/report/road'
import { getToken } from '@/utils/auth'
import { Upload } from '@element-plus/icons-vue'
import request from '@/utils/request'
import { ElMessage } from 'element-plus'
import { ref, computed, watch, onMounted } from 'vue'

// Props定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '数据导入'
  },
  selectedRoadId: {
    type: Number,
    default: null
  },
  selectedStatus: {
    type: String,
    default: ''
  }
})

// Emits定义
const emit = defineEmits(['update:modelValue', 'success', 'close'])

// 响应式数据
const dialogVisible = ref(false)
const roadId = ref(null)
const selectedConditionType = ref('')
const roadList = ref([])
const fileList = ref([])
const uploading = ref(false)
const resultDialogVisible = ref(false)
const uploadResult = ref(null)
const uploadRef = ref(null)

// 基础URL
const baseApiUrl = import.meta.env.VITE_APP_BASE_API

// 路面技术状况选项
const conditionOptions = [
  { value: 'PQI', label: 'PQI - 路面质量指数' },
  { value: 'PCI', label: 'PCI - 路面状况指数' },
  { value: 'RQI', label: 'RQI - 平整度指数' },
  { value: 'RDI', label: 'RDI - 车辙指数' },
  { value: 'PBI', label: 'PBI - 路面跳车指数' },
  { value: 'SRI', label: 'SRI - 抗滑指数' },
  { value: 'PSSI', label: 'PSSI - 路面强度指数' }
]

// API路径映射
const apiMapping = {
  'PQI': '/smart/road/check/pqi/importData',
  'PCI': '/smart/road/check/pci/importData',
  'RQI': '/smart/road/check/rqi/importData',
  'RDI': '/smart/road/check/rd/importData',
  'PBI': '/smart/road/check/pbi/importData',
  'SRI': '/smart/road/check/sfc/importData',
  'PSSI': '/smart/road/check/pssi/importData'
}

// 模板文件映射
const templateMapping = {
  'PQI': '/smart/road/check/pqi/importTemplate',
  'PCI': '/smart/road/check/pci/importTemplate',
  'RQI': '/smart/road/check/rqi/importTemplate',
  'RDI': '/smart/road/check/rd/importTemplate',
  'PBI': '/smart/road/check/pbi/importTemplate',
  'SRI': '/smart/road/check/sfc/importTemplate',
  'PSSI': '/smart/road/check/pssi/importTemplate'
}

// 计算属性
const dialogTitle = computed(() => props.title)
const canUpload = computed(() => (roadId.value || props.selectedRoadId) && selectedConditionType.value && fileList.value.length > 0)
const canDownloadTemplate = computed(() => selectedConditionType.value)
const uploadUrl = computed(() => selectedConditionType.value ? baseApiUrl + apiMapping[selectedConditionType.value] : '')
const uploadHeaders = computed(() => ({
  'Authorization': 'Bearer ' + getToken()
}))
const uploadData = computed(() => ({
  roadId: roadId.value || props.selectedRoadId
}))

// 监听器
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    initializeData()
  }
}, { immediate: true })

watch(() => dialogVisible.value, (val) => {
  emit('update:modelValue', val)
})

watch(() => props.selectedRoadId, (val) => {
  if (val) {
    roadId.value = val
  }
})

watch(() => props.selectedStatus, (val) => {
  if (val) {
    selectedConditionType.value = val
  }
})

// 方法
const initializeData = () => {
  loadRoadList()
  resetForm()
  
  if (props.selectedRoadId) {
    roadId.value = props.selectedRoadId
  }
  
  if (props.selectedStatus) {
    selectedConditionType.value = props.selectedStatus
  }
}

const getStatusLabel = (statusKey) => {
  const status = conditionOptions.find(item => item.value === statusKey)
  return status ? status.label : statusKey
}

const loadRoadList = async () => {
  try {
    const response = await listRoad()
    roadList.value = response.rows || []
  } catch (error) {
    console.error('加载路线列表失败:', error)
    ElMessage.error('加载路线列表失败')
  }
}

const handleRoadChange = (val) => {
  roadId.value = val
}

const handleConditionChange = (val) => {
  selectedConditionType.value = val
}

const downloadTemplate = () => {
  if (!canDownloadTemplate.value) {
    ElMessage.warning('请先选择路面技术状况类型')
    return
  }
  
  const templateUrl = templateMapping[selectedConditionType.value]
  if (!templateUrl) {
    ElMessage.error('该状况类型暂不支持模板下载')
    return
  }

  // 使用带认证的下载方式
  const link = document.createElement('a')
  link.style.display = 'none'
  
  // 创建带认证的POST请求
  request({
    url: templateUrl,
    method: 'post',
    responseType: 'blob'
  }).then(response => {
    // 创建下载链接
    const blob = new Blob([response])
    const url = window.URL.createObjectURL(blob)
    link.href = url
    
    // 设置下载文件名
    const fileName = `${getStatusLabel(selectedConditionType.value)}_导入模板.xlsx`
    link.download = fileName
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 释放URL对象
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('模板下载成功')
  }).catch(error => {
    console.error('模板下载失败:', error)
    ElMessage.error('模板下载失败: ' + (error.message || '未知错误'))
  })
}

const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                 file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  
  return false // 阻止自动上传
}

const handleFileChange = (file, uploadFileList) => {
  fileList.value = uploadFileList
}

const handleRemove = (file, uploadFileList) => {
  fileList.value = uploadFileList
}

const handleConfirmUpload = async () => {
  if (!canUpload.value) {
    ElMessage.warning('请完善上传信息')
    return
  }

  uploading.value = true
  
  try {
    const formData = new FormData()
    formData.append('file', fileList.value[0].raw)
    formData.append('roadId', roadId.value || props.selectedRoadId)

    const apiUrl = apiMapping[selectedConditionType.value]
    
    const response = await request({
      url: apiUrl,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    uploadResult.value = response
    resultDialogVisible.value = true
    
    emit('success', response)
    
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败: ' + (error.message || '未知错误'))
  } finally {
    uploading.value = false
  }
}

const resetForm = () => {
  if (!props.selectedRoadId) {
    roadId.value = null
  }
  if (!props.selectedStatus) {
    selectedConditionType.value = ''
  }
  fileList.value = []
  uploading.value = false
  uploadResult.value = null
  resultDialogVisible.value = false
}

const closeDialog = () => {
  resetForm()
  dialogVisible.value = false
  emit('close')
}

const getResultSummary = () => {
  if (!uploadResult.value) return '导入结果'
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  if (uploadResult.value.msg) {
    return uploadResult.value.msg
  }
  if (data.msg) {
    return data.msg
  }
  return '导入结果'
}

const getResultType = () => {
  if (!uploadResult.value) return 'error'
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  if (uploadResult.value.code === 200 || data.status === 0) {
    return 'success'
  }
  return 'error'
}

const getErrorCount = () => {
  if (!uploadResult.value) return 0
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  if (data.errors && Array.isArray(data.errors)) {
    return data.errors.length
  }
  return 0
}

const getErrorList = () => {
  if (!uploadResult.value) return []
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  if (data.errors && Array.isArray(data.errors)) {
    return data.errors
  }
  return []
}

const hasErrors = () => {
  if (!uploadResult.value) return false
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  return data.errors && Array.isArray(data.errors) && data.errors.length > 0
}

const hasColumn = (column) => {
  const errorList = getErrorList()
  return errorList.some(error => error && error[column] !== undefined)
}

const isSuccessful = () => {
  if (!uploadResult.value) return false
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  const successCount = data.successCount || 0
  const failCount = data.failCount || 0
  const totalCount = data.totalCount || 0
  
  // 如果有总数且成功数等于总数，或者有成功数且失败数为0
  return (totalCount > 0 && successCount === totalCount) || (successCount > 0 && failCount === 0)
}

const showDebugInfo = () => {
  return import.meta.env.DEV
}

const getTotalCount = () => {
  if (!uploadResult.value) return 0
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  return data.totalCount || 0
}

const getSuccessCount = () => {
  if (!uploadResult.value) return 0
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  return data.successCount || 0
}

const getFailCount = () => {
  if (!uploadResult.value) return 0
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  return data.failCount || 0
}

const getDetailMessage = () => {
  if (!uploadResult.value) return ''
  
  // 处理嵌套数据结构
  const data = uploadResult.value.data || uploadResult.value
  
  return data.msg || ''
}

// 生命周期
onMounted(() => {
  if (props.modelValue) {
    initializeData()
  }
})
</script>

<style scoped>
.enhanced-upload-container {
  padding: 20px;
}

.upload-area {
  margin: 20px 0;
  padding: 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  text-align: center;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409EFF;
}

.upload-tip {
  font-size: 14px;
  color: #606266;
  margin-top: 10px;
}

.template-download {
  margin-bottom: 15px;
  text-align: center;
}

.el-descriptions {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-upload-dragger) {
  width: 100%;
}
</style> 