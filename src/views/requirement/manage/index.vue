<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="medium" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备名称" prop="name">
        <el-input
            v-model="queryParams.name"
            placeholder="请输入设备名称"
            clearable
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备类型" prop="type">
        <el-input
          v-model="queryParams.type"
          placeholder="请输入设备类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备数据来源" prop="dataFrom">
        <el-select v-model="queryParams.dataFrom" placeholder="请选择设备数据来源" clearable>
          <el-option label="mqtt" value="mqtt" />
          <el-option label="http" value="http" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['requirement:manage:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['requirement:manage:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['requirement:manage:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['requirement:manage:import']"
        >导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="requirementDataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" :selectable="rowSelectable" />
      <el-table-column v-for="(property, index) in tablePropertyList"
        :key="property.id" :label="property.name" align="center" :prop="property.property" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button
            size="mini"
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['requirement:manage:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['requirement:manage:remove']"
          >删除</el-button>
          <el-button
              size="mini"
              type="text"
              icon=""
              @click="handleView(scope.row)"
              v-hasPermi="['requirement:manage:query']"
          >查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList" />

    <!-- 添加或修改检测数据记录对话框 -->
    <el-dialog :title="title" v-model="open" width="70%" @close="cancel" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="24">
            <el-form-item
                v-for="(property, index) in addOrUpdatePropertyList"
                :key="property.key"
                :label="property.name"
                :prop="property.property"
                >
              <el-select v-if="property.property == 'roadId'" v-model="form.roadId" placeholder="请选择路线" filterable clearable style="width: 100%">
                <el-option
                    v-for="road in roadList"
                    :key="road.id"
                    :label="`${road.companyName} - ${road.roadCode} (${road.roadName})`"
                    :value="road.id"
                >
                  <span style="float: left">{{ road.companyName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ road.roadCode }} - {{ road.roadName }}</span>
                </el-option>
              </el-select>
              <el-input v-else :disabled="editData != null && property.property == 'id'" v-model="form[property.property]" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保存设备</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog title="检测数据详情" v-model="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item
            v-for="(property, index) in viewPropertyList"
            :key="property.key"
            :label="property.name"
        >{{viewForm[property.property]}}
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRequirementData, getRequirementData, delRequirementData, addRequirementData, updateRequirementData } from "@/api/requirement/manage";
import {listAllRoad} from '@/api/report/road'
import { getToken } from "@/utils/auth";
import {parseTime} from "../../../utils/ruoyi";
import { Plus } from '@element-plus/icons-vue';

export default {
  name: "manage",
  components: {
    // RepairRecordForm,
    Plus
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 桥梁风险检查记录表格数据
      requirementDataList: [],
      // 映射列表
      propertyList: [
        {"property": "code", "name": "设备编码"},
        {"property": "name", "name": "设备名称"},
        {"property": "type", "name": "设备类型"},
        {"property": "typeName", "name": "设备类型"},
        {"property": "roadId", "name": "路线"},
        {"property": "roadName", "name": "路线"},
        {"property": "macAddress", "name": "mac地址"},
        {"property": "ip", "name": "ip"},
        {"property": "dataFrom", "name": "设备数据来源"},
        {"property": "topic", "name": "topic"},
        {"property": "remark", "name": "备注"},
        {"property": "createTime", "name": "创建时间"},
        {"property": "updateTime", "name": "更新时间"},
      ],
      tablePropertyList: [],
      addOrUpdatePropertyList: [],
      viewPropertyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      editData: null,
      // 查看详情弹出层
      viewOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // 设备名称
        name: null,
        // 设备类型
        type: null,
        // 数据来源
        dataFrom: null,
      },
      // 表单参数
      form: {},
      // 查看表单
      viewForm: {},
      // 表单校验
      rules: {
        key: [
          { required: true, message: "业务key不能为空", trigger: "change" }
        ],
        bridgeName: [
          { required: true, message: "桥梁名称不能为空", trigger: "blur" }
        ],
        centerPile: [
          { required: true, message: "中心桩号不能为空", trigger: "blur" }
        ]
      },
      // 路线数据
      roadList: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: import.meta.env.VITE_APP_BASE_API + "/requirement/manage/importData"
      },
      // 图片上传相关
      token: getToken(),
    };
  },
  computed: {
    // 检查当前用户是否为项目经理
  },
  created() {
    this.editData = null;
    this.filterPropertyList();
    this.getList();
  },
  methods: {
    parseTime,
    filterPropertyList() {
      this.tablePropertyList = this.propertyList.filter(column => {
        // 这里是过滤条件，返回 true 表示保留，false 表示跳过
        return column.property !== 'roadId'
            && column.property !== 'type';
      });

      this.addOrUpdatePropertyList = this.propertyList.filter(column => {
        // 这里是过滤条件，返回 true 表示保留，false 表示跳过
        return column.property !== 'roadName'
            && column.property !== 'typeName'
            && column.property !== 'creator'
            && column.property !== 'modifier'
            && column.property !== 'createTime'
            && column.property !== 'updateTime'
            ;
      });

      this.viewPropertyList = this.propertyList.filter(column => {
        // 这里是过滤条件，返回 true 表示保留，false 表示跳过
        return column.property !== 'roadId';
      });
    },
    /** 查询设备数据列表 */
    getList() {
      this.loading = true;
      listRequirementData(this.queryParams).then(response => {
        this.requirementDataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
      this.editData = null;
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: null,
        name: null,
        type: null,
        dataFrom: null,
      };
      this.resetForm("form");
      // 重置维修记录表单
      // this.$nextTick(() => {
      //   if (this.$refs.repairRecordForm) {
      //     this.$refs.repairRecordForm.resetForm();
      //   }
      // });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.key)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    listAllRoad() {
      listAllRoad({}).then(response => {
        if (response && response.rows) {
          this.roadList = response.rows;
        } else {
          this.$modal.msgWarning("获取路线失败");
        }
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备数据记录";
      this.listAllRoad()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.title = "修改设备数据记录";
      this.editData = row;
      this.form = this.editData;
      this.listAllRoad();
    },
    /** 查看按钮操作 */
    handleView(row) {
      getRequirementData(row.id).then(response => {
        if (response && response.data) {
          this.viewForm = response.data;
          this.viewOpen = true;
        } else {
          this.$modal.msgWarning("获取详情失败");
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRequirementData(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRequirementData(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids.join(',');
      this.$modal.confirm('是否确认删除设备编号为"' + ids + '"的数据项？').then(function() {
        return delRequirementData(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/requirement/manage/export', {
        ...this.queryParams
      }, `设备数据_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "设备数据导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('/requirement/manage/importTemplate', {}, `设备数据导入模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },

    // 行选择控制 - 只有待审核状态的记录才能被选中
    rowSelectable(row, index) {
      return true; // 只有待审核状态(0)才能选中
    },
    //
    // // 审核处理
    // handleAudit() {
    //   if (this.ids.length === 0) {
    //     this.$modal.msgError("请选择要审核的记录");
    //     return;
    //   }
    //   this.auditForm.ids = [...this.ids];
    //   this.auditOpen = true;
    // },
    //
    // // 审核状态变化处理
    // handleStatusChange() {
    //   // 当状态变化时，重新验证备注字段
    //   this.$nextTick(() => {
    //     this.$refs.auditForm.validateField('remark');
    //   });
    // },
    //
    // // 提交审核
    // submitAudit() {
    //   this.$refs.auditForm.validate(valid => {
    //     if (valid) {
    //       auditCheckBridge(this.auditForm).then(response => {
    //         this.$modal.msgSuccess("审核成功");
    //         this.auditOpen = false;
    //         this.getList();
    //       });
    //     }
    //   });
    // },
    //
    // // 取消审核
    // cancelAudit() {
    //   this.auditOpen = false;
    //   this.auditForm = {
    //     ids: [],
    //     checkStatus: 1,
    //     remark: ''
    //   };
    // }

  }
};
</script>

<style>
.upload-demo .el-upload--picture-card {
  width: 180px;
  height: 180px;
  line-height: 180px;
}
.upload-demo .el-upload-list--picture-card .el-upload-list__item {
  width: 180px;
  height: 180px;
}
</style>
