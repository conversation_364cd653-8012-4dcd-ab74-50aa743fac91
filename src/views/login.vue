<template>

  <div class="login">
    <!--<dv-border-box-11 title="环保数字化平台" :color="['white', 'green']">-->
    <el-row>
      <el-col :span="24">
          <div style="width:100%;text-align:center;color: white;margin-top: 70px;font-size: 38px;font-weight:500;  letter-spacing: 2px;font-family: 'Microsoft YaHei UI', 'Microsoft YaHei', DengXian, SimSun, 'Segoe UI', Tahoma, Helvetica, sans-serif;">
            环保数字化平台
          </div>
      </el-col>
    </el-row>
    <el-row>
       <el-col :span="24" style="margin-left: 39%;margin-top: 140px;">
         <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
           <dv-border-box-8 :color="['#006FFF']" style="width:400px;">
             <h3 class="title" style="font-size: 24px;margin-top: 20px;">欢 迎 登 录</h3>
             <h5 class="title" style="color: #707070;margin-top: -20px;">— welcome to login —</h5>
             <el-form-item prop="username">
               <el-input
                   v-model="loginForm.username"
                   type="text"
                   size="large"
                   auto-complete="off"
                   placeholder="账号"
               >
                 <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
               </el-input>
             </el-form-item>
             <el-form-item prop="password">
               <el-input
                   v-model="loginForm.password"
                   type="password"
                   size="large"
                   auto-complete="off"
                   placeholder="密码"
                   @keyup.enter="handleLogin"
               >
                 <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
               </el-input>
             </el-form-item>
             <!--<el-form-item prop="code" v-if="captchaEnabled">
               <el-input
                 v-model="loginForm.code"
                 size="large"
                 auto-complete="off"
                 placeholder="验证码"
                 style="width: 63%"
                 @keyup.enter="handleLogin"
               >
                 <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
               </el-input>
               <div class="login-code">
                 <img :src="codeUrl" @click="getCode" class="login-code-img"/>
               </div>
             </el-form-item>-->
             <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;"><span style="color: white;font-size: 13px;">记住密码</span></el-checkbox>
             <el-form-item style="width:100%;">
               <el-button
                   :loading="loading"
                   size="large"
                   type="primary"
                   style="width:100%;"
                   @click.prevent="handleLogin"
               >
                 <span v-if="!loading" style="font-size: 16px;font-weight: 500;">登 录</span>
                 <span v-else style="font-size: 16px;font-weight: 500;">登 录 中...</span>
               </el-button>
               <div style="float: right;" v-if="register">
                 <router-link class="link-type" :to="'/register'">立即注册</router-link>
               </div>
             </el-form-item>
           </dv-border-box-8>

         </el-form>
       </el-col>
    </el-row>
    <!--  底部  -->
    <div class="el-login-footer">
      <!--<span>Copyright © 2018-2022 jsyc.com All Rights Reserved.</span>-->
      <!--<span>安徽省七星工程测试有限公司.</span>-->
    </div>
    <!--</dv-border-box-11>-->
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "admin",
  password: "admin123",
  rememberMe: false,
  code: "",
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: false, trigger: "change", message: "请输入验证码" }]
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(false);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        router.push({ path: redirect.value || "/" });
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode();
        }
      });
    }
  });
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ?  false: res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

//getCode();
getCookie();
</script>

<style lang='scss' scoped>
.login {
  //display: flex;
  //justify-content: center;
  //position: absolute;
  //top: 250px;
  //right: 113px;
  //align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.png");
  background-size: 100% 100%;//cover;
}
:deep(.dv-border-box-11 .border-box-content) {
  justify-content: center !important;
  align-items: center !important;
  display: flex !important;
}
:deep(.login-form .input-icon[data-v-d0e06bca]){
  color: #006FFF;
}
:deep(.dv-border-box-8 .border-box-content){
  padding: 50px;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: white;
}

.login-form {
  //position: absolute;
  //top: 250px;
  //right: 113px;
  border-radius: 6px;
  /*background: #ffffff;*/
  background-color: rgba(0, 111, 255, 0.1);
  width: 400px;
  /*padding: 25px 25px 5px 25px;*/
  .el-input {
    height: 40px;
    input {
      height: 40px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 14px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}
:deep(.el-input__wrapper){
  background: transparent !important;
  border: 1px solid #006FFF !important;
  box-shadow:unset ;
}
:deep(.el-input__inner) {
  color: white;
}
:deep(.el-button--primary){
  --el-button-bg-color: #006FFF;
}
:deep(.el-checkbox__input.is-checked .el-checkbox__inner){
  background-color:#006FFF;
  border-color:#006FFF;
}
:deep(.el-button){
  border:unset;
}
</style>
