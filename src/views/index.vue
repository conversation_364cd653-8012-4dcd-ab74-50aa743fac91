<template>
  <div class="gis-map">
    <div id="gisMapContainer"></div>
  </div>
</template>
<script  setup>
import { ref, onMounted } from "vue";
const map = ref();
const initMap = async (lat = 117.014855, lng = 31.859474, zoom = 8) => {
  map.value = new BMapGL.Map("gisMapContainer"); //创建地图实例
  let point = new BMapGL.Point(lat, lng); //地图中心点
  map.value.centerAndZoom(point, zoom); //地图初始化，同时设置地图展示级别
  map.value.enableScrollWheelZoom(true); //使用鼠标滚轮控制缩放
};

onMounted(() => {
  nextTick(() => {
    initMap();
  });
});
</script>
<style lang="scss" scoped>
.gis-map {
  width: calc(100vw - 160px);
  height: calc(100vh - 80px);
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  #gisMapContainer {
    flex: 1;
  }
  
}
</style>