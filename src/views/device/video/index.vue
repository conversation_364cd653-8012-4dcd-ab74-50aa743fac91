<template>
  <div class="app-container">
    <el-card>
      <el-form
        :model="form"
        ref="ruleFormRef"
        label-width="auto"
        :inline="true"
      >
        <el-form-item label="监测点位" prop="monitoringType">
          <el-select v-model="form.monitoringType" placeholder="请选择监测类型">
            <el-option
              v-for="item in classifications"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围" prop="chosenDate" chosenDate>
          <el-date-picker
            v-model="form.chosenDate"
            type="date"
            placeholder="查询日期"
          />
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" @click="submitForm()"> 查询 </el-button>
          <el-button @click="resetForm(ruleFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <br />
    <br />
    <el-card>
      <div class="real-time--content">
        <el-button type="primary" @click="submitForm()"> 上传视频 </el-button>
      </div>
      <div class="video--content">
        <!-- <video
          class="video--content--video"
          v-for="item in 26"
          :key="item"
          controls
        >
          <source src="movie.mp4" type="video/mp4" />
          <source src="movie.ogg" type="video/ogg" />
          您的浏览器不支持 HTML5 video 标签。
        </video> -->
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import CONSTANTS from "@/common/constants";
import type { TabsPaneContext, FormInstance } from "element-plus";
const form = ref<any>({});
const classifications = ref(CONSTANTS.classifications);

const ruleFormRef = ref<TabsPaneContext>(null);
const submitForm = () => {
  console.log("submit!");
};
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
</script>
<style lang="scss" scoped>
.real-time--content {
 
}
.video--content {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  .video--content--video {
    width: 33.333%;
    height: 200px;
    padding: 12px;
    border-radius: 12px;
  }
}
</style>