<template>
  <el-dialog
    :title="'选择内容'"
    width="1000"
    destroy-on-close
    v-model="open"
    append-to-body
  >
    <el-scrollbar :height="'68vh'"> 选择内容 </el-scrollbar>
  </el-dialog>
</template>
  <script setup lang="ts">
import { defineAsyncComponent, ref, onMounted } from "vue";
import { tableConfig } from "./constants";
const tableData = ref<Array<any>>([]);
const loading = ref<boolean>(false);
const total = ref<number>(1);
const pageNum = ref<number>(1);
const pageSize = ref<number>(10);
const init = async (id: any) => {
  try {
    loading.value = true;

    loading.value = false;
  } catch (err) {
    loading.value = false;
    console.log(err);
  }
};

const open = ref(false);
const openDialog = (id: any) => {
  init("");
  open.value = true;
};
const getList = () => {
  console.log("getList");
};

// 暴露变量
defineExpose({
  openDialog,
});
onMounted(async () => {});
</script>
  
      