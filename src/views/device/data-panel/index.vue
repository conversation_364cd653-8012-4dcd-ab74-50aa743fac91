<template>
  <div class="app-container">
    <el-card>
      <el-form
        :model="form"
        ref="ruleFormRef"
        label-width="auto"
        :inline="true"
      >
        <el-form-item label="网关" prop="monitoringType">
          <el-select v-model="form.monitoringType" placeholder="请选择监测类型">
            <el-option
              v-for="item in classifications"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围" prop="chosenDate" chosenDate>
          <el-date-picker
            v-model="form.chosenDate"
            type="date"
            placeholder="查询日期"
          />
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" @click="submitForm()"> 查询 </el-button>
          <el-button @click="resetForm(ruleFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <br />
    <br />
    <el-card>
      <div class="real-time--content">
        <el-tabs
          v-model="activeName"
          class="demo-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane label="实时数据" name="realTime"></el-tab-pane>
          <el-tab-pane label="历史数据" name="history"></el-tab-pane>
        </el-tabs>
        <div class="ml-4">
          <el-radio-group class="" v-model="viewType">
            <el-radio-button label="图表模式" value="图表模式" />
            <el-radio-button label="列表模式" value="列表模式" />
          </el-radio-group>
          &nbsp; &nbsp;
          <el-button type="success" @click="showSelectView()"> 显示内容 </el-button>
        </div>

        <EchartsView v-show="viewType == '图表模式'" />
        <TableList v-show="viewType == '列表模式'" />
        <select-view ref="refSelectView" />
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import CONSTANTS from "@/common/constants";
import type { TabsPaneContext, FormInstance } from "element-plus";
const EchartsView = defineAsyncComponent(
  () => import("./compoments/ecahrtsVies/index.vue")
);
const TableList = defineAsyncComponent(
  () => import("./compoments/tableList/index.vue")
);
const SelectView = defineAsyncComponent(
  () => import("./compoments/select-view/index.vue")
);
const viewType = ref("图表模式");
const form = ref<any>({});
const classifications = ref(CONSTANTS.classifications);
const filteredList = ref<any[]>([]);
const activeName = ref("realTime");
const serviceAreaChanged = (val: any) => {
  console.log(val);
};
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};

const ruleFormRef = ref<TabsPaneContext>(null);
const submitForm = () => {
  console.log("submit!");
};
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
const refSelectView = ref<any>(null);
const showSelectView = () => {
  refSelectView.value.openDialog()
};
</script>
<style lang="scss" scoped>
.real-time--content {
  position: relative;
  // padding-top: 40px;
  .ml-4 {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
  }
}
</style>