<template>
  <div class="app-container">
    <el-card>
      <el-form
        :model="form"
        ref="ruleFormRef"
        label-width="auto"
        :inline="true"
      >
        <el-form-item label="地区" prop="monitoringType">
          <el-select v-model="form.monitoringType" placeholder="请选择监测类型">
            <el-option
              v-for="item in classifications"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="监测点位" prop="chosenDate" chosenDate>
          <el-date-picker
            v-model="form.chosenDate"
            type="date"
            placeholder="查询日期"
          />
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" @click="submitForm()"> 查询 </el-button>
          <el-button @click="resetForm(ruleFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <br />
    <br />
    <el-card>
      <div class="real-time--content"></div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import CONSTANTS from "@/common/constants";
import type { TabsPaneContext, FormInstance } from "element-plus";
const form = ref<any>({});
const classifications = ref(CONSTANTS.classifications);

const ruleFormRef = ref<TabsPaneContext>(null);
const submitForm = () => {
  console.log("submit!");
};
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
</script>
<style lang="scss" scoped>
.real-time--content {
  position: relative;
  // padding-top: 40px;
  .ml-4 {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
  }
}
</style>