<template>
  <div class="app-container">
    <el-card>
      <el-form :model="form" label-width="auto" :inline="true">
        <el-form-item label="监测类型">
          <el-select v-model="form.monitoringType" placeholder="请选择监测类型">
            <el-option
              v-for="item in classifications"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="监测点位">
          <el-select
            v-model="form.name"
            filterable
            placeholder="请选择"
            @change="serviceAreaChanged"
          >
            <el-option
              v-for="item in filteredList"
              :key="item.code"
              :label="item.name"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="查询日期" chosenDate>
          <el-date-picker
            v-model="form.chosenDate"
            type="date"
            placeholder="查询日期"
          />
        </el-form-item>
      </el-form>
    </el-card>
    <br />
    <el-card></el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import CONSTANTS from "@/common/constants";
const form = ref<any>({});
const classifications = ref(CONSTANTS.classifications);
const filteredList = ref<any[]>([]);
const serviceAreaChanged = (val: any) => {
  console.log(val);
};
</script>