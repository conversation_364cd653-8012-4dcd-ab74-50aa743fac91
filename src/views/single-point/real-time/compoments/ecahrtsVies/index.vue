<template>
  <div>
    <EchartsComp
      ref="refsEchartsComp"
      :option="optionData"
      :height="'40vh'"
    ></EchartsComp>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import { selectMonitorListForChart } from "@/api/single-point/real-time";
const EchartsComp = defineAsyncComponent(
  () => import("@/components/echarts-comp/index.vue")
);
import { option } from "./constants";
let optionData = option();
// console.log(option());
const echartsData = ref<any[]>([]);
const getList = async (form: any) => {
    try {
      const { data } = await selectMonitorListForChart({
        ...form,
      });
      echartsData.value = data.list;
    } catch (err) {
      console.log(err);
    }
};
const handleClick = (params) => {
  console.log(params);
};
defineExpose({
  getList,
});
</script>