<template>
  <div v-loading="loading" element-loading-text="正在加载中...">
    <el-table :data="tableData" :style="{ height: '50vh' }">
      <el-table-column type="index" fixed="left" label="序号" width="50" />
      <el-table-column
        v-for="(item, index) in config"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :min-width="item?.width || 110"
      >
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      v-model:page="pageNum"
      v-model:limit="pageSize"
      @pagination="getList(formValue)"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import {
  selectMonitorListByPage,
  getFieldsByType,
} from "@/api/single-point/real-time";
import { tableConfig } from "./constants";
const tableData = ref<any[]>([]);
const total = ref<number>(0);
const pageNum = ref<number>(1);
const pageSize = ref<number>(10);
const loading = ref<boolean>(false);
let formValue = ref<any>({});
const config = ref<any>(tableConfig);
const getList = async (form: any) => {
  try {
    loading.value = true;
    formValue.value = form;
    const { data } = await selectMonitorListByPage({
      ...formValue.value,
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    });
    tableData.value = data.list;
    total.value = data.total;
    loading.value = false;
  } catch (err) {
    console.log(err);
    loading.value = false;
  }
};
const _getFieldsByType = async (bTypeObjs = {}) => {
  try {
    const { data } = await getFieldsByType(bTypeObjs);
    config.value = data;
    config.value.forEach((item) => {
      item.label = `${item.fieldName} ${item?.unit || ""}`;
      item.prop = item.fieldCode;
    });
  } catch (err) {
    console.log(err);
  }
};
defineExpose({
  getList,
  _getFieldsByType,
});
</script>