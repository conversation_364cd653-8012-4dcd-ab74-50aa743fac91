<template>
  <div>
    <el-table :data="tableData" :style="{ height: '50vh' }">
      <el-table-column type="index" fixed="left" label="序号" width="50" />
      <el-table-column
        v-for="(item, index) in tableConfig"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :min-width="item?.width || 120"
      >
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      v-model:page="pageNum"
      v-model:limit="pageSize"
      @pagination="getList"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { tableConfig } from "./constants";
const tableData = ref<any[]>([]);
const total = ref<number>(1);
const pageNum = ref<number>(1);
const pageSize = ref<number>(10);
const getList = () => {
  console.log("getList");
};
</script>