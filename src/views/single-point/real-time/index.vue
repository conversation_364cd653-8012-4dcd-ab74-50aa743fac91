<template>
  <div class="app-container">
    <el-card>
      <el-form
        :model="form"
        ref="ruleFormRef"
        :inline="true"
        label-width="auto"
      >
        <el-form-item label="监测类型"  prop="monitoringType">
          <el-select v-model="form.monitoringType" placeholder="请选择监测类型">
            <el-option
              v-for="item in classifications"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="监测点位" prop="name">
          <el-select
            v-model="form.name"
            filterable
            placeholder="请选择"
            @change="serviceAreaChanged"
          >
            <el-option
              v-for="item in filteredList"
              :key="item.code"
              :label="item.name"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="activeName == 'history'" label="查询日期" prop="chosenDate" chosenDate>
          <el-date-picker
            v-model="form.chosenDate"
            type="date"
            placeholder="查询日期"
          />
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" @click="submitForm()"> 查询 </el-button>
          <el-button @click="resetForm(ruleFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <br />
    <el-card>
      <div class="real-time--content">
        <!-- <el-tabs
          v-model="activeName"
          class="demo-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane label="实时数据" name="realTime"></el-tab-pane>
          <el-tab-pane label="历史数据" name="history"></el-tab-pane>
        </el-tabs> -->
        <el-radio-group class="ml-4" v-model="viewType">
          <el-radio-button label="图表模式" value="图表模式" />
          <el-radio-button label="列表模式" value="列表模式" />
        </el-radio-group>
        <EchartsView v-show="viewType == '图表模式'" />
        <TableList v-show="viewType == '列表模式'" />
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent } from "vue";
import type { TabsPaneContext, FormInstance } from "element-plus";
import CONSTANTS from "@/common/constants";
const EchartsView = defineAsyncComponent(
  () => import("./compoments/ecahrtsVies/index.vue")
);
const TableList = defineAsyncComponent(
  () => import("./compoments/tableList/index.vue")
);
const form = ref<any>({});
const classifications = ref(CONSTANTS.classifications);
const filteredList = ref<any[]>([]);
const activeName = ref("realTime");
const viewType = ref("图表模式");
const ruleFormRef = ref<TabsPaneContext>(null);
const serviceAreaChanged = (val: any) => {
  console.log(val);
};
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};
const submitForm = () => {
  console.log("submit!");
};
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
</script>
<style lang="scss" scoped>
.real-time--content {
  position: relative;
  padding-top: 40px;
  .ml-4 {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>