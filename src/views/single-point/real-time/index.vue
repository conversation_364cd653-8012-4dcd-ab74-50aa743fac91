<template>
  <div class="app-container">
    <el-card>
      <el-form
        :model="form"
        ref="ruleFormRef"
        :inline="true"
        label-width="auto"
      >
        <el-form-item label="监测类型" prop="type">
          <el-select
            v-model="form.type"
            @change="handelTypeChange"
            placeholder="请选择监测类型"
          >
            <el-option
              v-for="item in classifiConfig"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="监测点位" prop="systemCode">
          <el-select
            v-model="form.systemCode"
            filterable
            placeholder="请选择"
            @change="serviceAreaChanged"
          >
            <el-option
              v-for="item in filteredList"
              :key="item.code"
              :label="item.name"
              :value="item.systemCode"
            />
          </el-select>
        </el-form-item>
        <!-- :disabled-date="disabledDate" -->
        <el-form-item label="查询日期" prop="chosenDate" chosenDate>
          <el-date-picker
            v-model="form.chosenDate"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" @click="submitForm()"> 查询 </el-button>
          <el-button @click="resetForm(ruleFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <br />
    <el-card>
      <div class="real-time--content">
        <!-- <el-tabs
          v-model="activeName"
          class="demo-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane label="实时数据" name="realTime"></el-tab-pane>
          <el-tab-pane label="历史数据" name="history"></el-tab-pane>
        </el-tabs> -->
        <el-radio-group class="ml-4" v-model="viewType" @change="submitForm">
          <el-radio-button label="图表模式" value="图表模式" />
          <el-radio-button label="列表模式" value="列表模式" />
        </el-radio-group>
        <EchartsView ref="refEchartsView" v-show="viewType == '图表模式'" />
        <TableList ref="refTableList" v-show="viewType == '列表模式'" />
      </div>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineAsyncComponent, onMounted } from "vue";
import {
  selectMonitorStationByType,
  selectTimeRange,
} from "@/api/single-point/real-time";
import type { TabsPaneContext, FormInstance } from "element-plus";
import { getStartOfDayFormatted, getCurrentTimeFormatted } from "@/utils/time";
import CONSTANTS from "@/common/constants";
const EchartsView = defineAsyncComponent(
  () => import("./compoments/ecahrtsVies/index.vue")
);
const TableList = defineAsyncComponent(
  () => import("./compoments/tableList/index.vue")
);
const form = ref<any>({
  type: 1,
  name: "",
  systemCode: "",
  chosenDate: [getStartOfDayFormatted(), getCurrentTimeFormatted()],
});
const classifiConfig = ref(CONSTANTS.classifiConfig);
const filteredList = ref<any[]>([]);
const activeName = ref("realTime");
const viewType = ref("图表模式");
const ruleFormRef = ref<TabsPaneContext>(null);
const startTime = ref(getStartOfDayFormatted());
const endTime = ref(getCurrentTimeFormatted());
const handelTypeChange = (val: any) => {
  selectMonitorStationByType({ type: val }).then((res) => {
    filteredList.value = res.data;
    if (filteredList.value.length) {
      form.value.systemCode = filteredList.value[0].systemCode;
    }
  });
};
const serviceAreaChanged = async (val: any) => {
  try {
    selectTimeRange({ type: form.value.type, systemCode: val }).then((res) => {
      startTime.value = res.data?.startTime;
      endTime.value = res.data?.endTime;
    });
    refTableList.value?._getFieldsByType({
      type: form.value.type,
      systemCode: form.value.systemCode,
    });
  } catch (err) {
    console.log(err);
  }
};
// const disabledDate = (time: Date) => {
//   const start = new Date(startTime.value);
//   const end = new Date(endTime.value);
//   return time.getTime() < start.getTime() || time.getTime() > end.getTime();
// };
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};
const refTableList = ref<any>(null);
const refEchartsView = ref<any>(null);
const submitForm = () => {
  let { type, systemCode, chosenDate } = form.value;
  let obsj = {
    // type,
    // systemCode,
    // startTime: chosenDate?.[0],
    // endTime: chosenDate?.[1],
    // 污水
    // type: 1,
    // startTime: "2022-02-11 16:46:10",
    // endTime: "2022-02-17 16:46:10",
    // systemCode: "88888881234568",
    // type: 2,
    // startTime: "2022-04-26 00:00:00",
    // endTime: "2022-04-26 12:28:27",
    // systemCode: "101010100",
    type: 1,
    startTime: "2022-02-17 16:30:10",
    endTime: "2022-02-17 16:46:10",
    systemCode: "88888881234568",
  };
  viewType.value === "列表模式" && refTableList.value?.getList(obsj);
  viewType.value === "图表模式" && refEchartsView.value?.getList(obsj);
};
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
};
const init = async () => {
  await handelTypeChange(form.value.type);

  await serviceAreaChanged(form.value.systemCode);
  await refTableList.value?._getFieldsByType({
    type: form.value.type,
    systemCode: form.value.systemCode,
  });
  await submitForm();
};
onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
.real-time--content {
  position: relative;
  padding-top: 40px;
  .ml-4 {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>