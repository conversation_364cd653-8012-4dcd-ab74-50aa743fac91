<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="站点名称" prop="name">
        <el-input
            v-model="queryParams.name"
            placeholder="请输入站点名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="站点编码" prop="code">
        <el-input
            v-model="queryParams.code"
            placeholder="请输入站点编码"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监测类型" prop="type">
        <el-select
            v-model="queryParams.type"
            placeholder="请选择监测类型"
            clearable
            style="width: 200px"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="省份" prop="province">
        <el-select
            v-model="queryParams.province"
            placeholder="请选择省份"
            clearable
            style="width: 200px"
        >
          <el-option
            v-for="province in provinces"
            :key="province.name"
            :label="province.name"
            :value="province.name"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="城市" prop="city">
        <el-input
            v-model="queryParams.city"
            placeholder="请输入城市"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
        >新增监测站点</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>



    <el-table v-loading="loading" :data="monitorStationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
<!--      <el-table-column label="序号" type="index" width="60" align="center" />-->
      <el-table-column label="站点名称" align="center" prop="name" width="220" :show-overflow-tooltip="true" />
      <el-table-column label="站点编码" align="center" prop="code" width="120" />
      <el-table-column label="监测类型" align="center" prop="type" width="100" />
      <el-table-column label="省份" align="center" prop="province" width="100" />
      <el-table-column label="城市" align="center" prop="city" width="100" />
      <el-table-column label="地区" align="center" prop="district" :show-overflow-tooltip="true" />
      <el-table-column label="经度" align="center" prop="lat" width="100" />
      <el-table-column label="纬度" align="center" prop="lon" width="100" />
      <el-table-column label="污染类型" align="center" prop="pollutionType" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="state" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.state === 1 ? 'success' : 'danger'">
            {{ scope.row.state === 1 ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="故障状态" align="center" prop="faulty" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.faulty === 0 ? 'success' : 'warning'">
            {{ scope.row.faulty === 0 ? '正常' : '故障' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
                <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button
                  type="text"
                  icon="View"
                  @click="handleView(scope.row)"
              >查看</el-button>
              <el-button
                  type="text"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                  type="text"
                  icon="Setting"
                  @click="handleMonitorFactor(scope.row)"
              >监测因子</el-button>
              <el-button
                  type="text"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
    </el-table>

    <pagination v-show="total > 0"
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList" />

    <!-- 添加或修改监测站点对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="stationRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="站点名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入站点名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站点编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入站点编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="监测类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择监测类型" style="width: 100%">
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="污染类型" prop="pollutionType">
              <el-input v-model="form.pollutionType" placeholder="请输入污染类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="省份" prop="province">
              <el-select v-model="form.province" placeholder="请选择省份" style="width: 100%" @change="handleProvinceChange">
                <el-option
                  v-for="province in provinces"
                  :key="province.name"
                  :label="province.name"
                  :value="province.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市" prop="city">
              <el-select v-model="form.city" placeholder="请选择城市" style="width: 100%" @change="handleCityChange">
                <el-option
                  v-for="city in formCities"
                  :key="city.name"
                  :label="city.name"
                  :value="city.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="地区" prop="district">
              <el-select v-model="form.district" placeholder="请选择地区" style="width: 100%">
                <el-option
                  v-for="district in formDistricts"
                  :key="district.name"
                  :label="district.name"
                  :value="district.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站点顺序" prop="stationindex">
              <el-input-number v-model="form.stationindex" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="经度" prop="lat">
              <el-input-number v-model="form.lat" :precision="6" :min="-180" :max="180" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="lon">
              <el-input-number v-model="form.lon" :precision="6" :min="-90" :max="90" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="state">
              <el-select v-model="form.state" placeholder="请选择状态" style="width: 100%">
                <el-option label="正常" :value="1" />
                <el-option label="停用" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="故障状态" prop="faulty">
              <el-select v-model="form.faulty" placeholder="请选择故障状态" style="width: 100%">
                <el-option label="正常" :value="0" />
                <el-option label="故障" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注信息" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看监测站点详情对话框 -->
    <el-dialog title="监测站点详情" v-model="viewOpen" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="站点名称">{{ viewForm.name }}</el-descriptions-item>
        <el-descriptions-item label="站点编码">{{ viewForm.code }}</el-descriptions-item>
        <el-descriptions-item label="监测类型">{{ viewForm.type }}</el-descriptions-item>
        <el-descriptions-item label="污染类型">{{ viewForm.pollutionType }}</el-descriptions-item>
        <el-descriptions-item label="省份">{{ viewForm.province }}</el-descriptions-item>
        <el-descriptions-item label="城市">{{ viewForm.city }}</el-descriptions-item>
        <el-descriptions-item label="地区">{{ viewForm.district }}</el-descriptions-item>
        <el-descriptions-item label="站点顺序">{{ viewForm.stationindex }}</el-descriptions-item>
        <el-descriptions-item label="经度">{{ viewForm.lat }}</el-descriptions-item>
        <el-descriptions-item label="纬度">{{ viewForm.lon }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewForm.state === 1 ? 'success' : 'danger'">
            {{ viewForm.state === 1 ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="故障状态">
          <el-tag :type="viewForm.faulty === 0 ? 'success' : 'warning'">
            {{ viewForm.faulty === 0 ? '正常' : '故障' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(viewForm.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注信息" :span="2">{{ viewForm.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 监测因子管理对话框 -->
    <el-dialog title="监测因子管理" v-model="factorDialogVisible" width="70%" append-to-body>
      <div style="margin-bottom: 16px;">
<!--        <el-button type="primary" @click="handleAddFactor">新增监测因子</el-button>-->
<!--        <el-button type="danger" :disabled="factorMultiple" @click="handleDeleteFactor">删除</el-button>-->
      </div>
      
      <el-table 
        v-loading="factorLoading" 
        :data="factorList" 
        @selection-change="handleFactorSelectionChange"
        max-height="400"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" type="index" width="60" align="center" />
        <el-table-column label="监测因子编码" prop="code" width="120" />
        <el-table-column label="监测因子名称" prop="name" width="150" />
        <el-table-column label="最小值" prop="minValue" width="100" />
        <el-table-column label="最大值" prop="maxValue" width="100" />
        <el-table-column label="单位" prop="unit" width="80" />
        <el-table-column label="设备ID" prop="sensorId" width="120" />
        <el-table-column label="因子排序" prop="factorIndex" width="100" />
        <el-table-column label="超标状态" prop="exceeded" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.exceeded === 0 ? 'success' : 'warning'">
              {{ scope.row.exceeded === 0 ? '正常' : '超标' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="故障状态" prop="faulty" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.faulty === 0 ? 'success' : 'danger'">
              {{ scope.row.faulty === 0 ? '正常' : '故障' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="text" @click="handleUpdateFactor(scope.row)">修改</el-button>
<!--            <el-button type="text" @click="handleDeleteFactor(scope.row)">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="factorTotal > 0"
        :total="factorTotal"
        v-model:page="factorQueryParams.pageNum"
        v-model:limit="factorQueryParams.pageSize"
        @pagination="getFactorList"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="factorDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改监测因子对话框 -->
    <el-dialog :title="factorTitle" v-model="factorFormVisible" width="600px" append-to-body>
      <el-form ref="factorFormRef" :model="factorForm" :rules="factorRules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="监测因子编码" prop="code">
              <el-input v-model="factorForm.code" placeholder="请输入监测因子编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监测因子名称" prop="name">
              <el-input v-model="factorForm.name" placeholder="请输入监测因子名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="最小值" prop="minValue">
              <el-input-number v-model="factorForm.minValue" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大值" prop="maxValue">
              <el-input-number v-model="factorForm.maxValue" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input v-model="factorForm.unit" placeholder="请输入单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备ID" prop="sensorId">
              <el-input v-model="factorForm.sensorId" placeholder="请输入设备ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="因子排序" prop="factorIndex">
              <el-input-number v-model="factorForm.factorIndex" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="超标状态" prop="exceeded">
              <el-select v-model="factorForm.exceeded" placeholder="请选择超标状态" style="width: 100%">
                <el-option label="正常" :value="0" />
                <el-option label="超标" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="故障状态" prop="faulty">
              <el-select v-model="factorForm.faulty" placeholder="请选择故障状态" style="width: 100%">
                <el-option label="正常" :value="0" />
                <el-option label="故障" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFactorForm">确 定</el-button>
          <el-button @click="cancelFactor">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="MonitorStation">
import { listMonitorStation, getMonitorStation, delMonitorStation, addMonitorStation, updateMonitorStation, exportMonitorStation } from "@/api/platform/monitorStation";
import { listMonitorFactor, getMonitorFactor, delMonitorFactor, addMonitorFactor, updateMonitorFactor } from "@/api/platform/monitorFactor";
import { parseTime } from "@/utils/ruoyi";
import { getCurrentInstance, reactive, ref, toRefs, computed, onMounted } from "vue";
import { getProvinces, getCitiesByProvinceName, getDistrictsByCityName } from "@/common/area";

const { proxy } = getCurrentInstance();

const monitorStationList = ref([]);
const open = ref(false);
const viewOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 监测因子相关变量
const factorDialogVisible = ref(false);
const factorFormVisible = ref(false);
const factorLoading = ref(false);
const factorList = ref([]);
const factorTotal = ref(0);
const factorTitle = ref('');
const factorIds = ref([]);
const factorMultiple = ref(true);
const currentSystemCode = ref('');

// 监测类型选项
const typeOptions = ref([
  { value: 'sewage', label: '污水类' },
  { value: 'air', label: '大气类' },
  { value: 'noise', label: '噪声类' },
  { value: 'solid', label: '固废类' }
]);

// 省市区数据
const provinces = ref([]);
const cities = ref([]);
const districts = ref([]);

// 表单中的省市区数据
const formCities = ref([]);
const formDistricts = ref([]);



const data = reactive({
  form: {},
  viewForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    code: null,
    type: null,
    province: null,
    city: null,
    district: null
  },
  rules: {
    name: [
      { required: true, message: "站点名称不能为空", trigger: "blur" }
    ],
    code: [
      { required: true, message: "站点编码不能为空", trigger: "blur" }
    ],
    type: [
      { required: true, message: "监测类型不能为空", trigger: "change" }
    ],
    province: [
      { required: true, message: "省份不能为空", trigger: "change" }
    ],
    city: [
      { required: true, message: "城市不能为空", trigger: "change" }
    ],
    district: [
      { required: true, message: "地区不能为空", trigger: "change" }
    ]
  },
  factorForm: {},
  factorQueryParams: {
    pageNum: 1,
    pageSize: 10,
    monitoringSystemSystemCode: null
  },
  factorRules: {
    code: [
      { required: true, message: "监测因子编码不能为空", trigger: "blur" }
    ],
    name: [
      { required: true, message: "监测因子名称不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, viewForm, rules, factorForm, factorQueryParams, factorRules } = toRefs(data);

// 方法
const handleProvinceChange = (value) => {
  form.value.city = '';
  form.value.district = '';
  formCities.value = [];
  formDistricts.value = [];
  
  if (value) {
    formCities.value = getCitiesByProvinceName(value);
  }
};

const handleCityChange = (value) => {
  form.value.district = '';
  formDistricts.value = [];
  
  if (value && form.value.province) {
    formDistricts.value = getDistrictsByCityName(form.value.province, value);
  }
};

/** 查询监测站点列表 */
function getList() {
  loading.value = true;
  listMonitorStation(queryParams.value).then(response => {
    monitorStationList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    code: null,
    type: null,
    province: null,
    city: null,
    district: null,
    lat: null,
    lon: null,
    pollutionType: null,
    state: 1,
    faulty: 0,
    stationindex: 0,
    remark: null
  };
  formCities.value = [];
  formDistricts.value = [];
  if (proxy.resetForm) {
    proxy.resetForm("stationRef");
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  if (proxy.resetForm) {
    proxy.resetForm("queryRef");
  }
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加监测站点";
}

/** 查看按钮操作 */
function handleView(row) {
  const id = row.id || ids.value;
  getMonitorStation(id).then(response => {
    viewForm.value = response.data;
    viewOpen.value = true;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getMonitorStation(id).then(response => {
    form.value = response.data;
    // 加载对应的城市和地区列表
    if (response.data.province) {
      formCities.value = getCitiesByProvinceName(response.data.province);
    }
    if (response.data.province && response.data.city) {
      formDistricts.value = getDistrictsByCityName(response.data.province, response.data.city);
    }
    open.value = true;
    title.value = "修改监测站点";
  });
}

/** 提交按钮 */
function submitForm() {
  if (proxy.$refs && proxy.$refs["stationRef"]) {
    proxy.$refs["stationRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateMonitorStation(form.value).then(response => {
            if (proxy.$modal) {
              proxy.$modal.msgSuccess("修改成功");
            }
            open.value = false;
            getList();
          });
        } else {
          addMonitorStation(form.value).then(response => {
            if (proxy.$modal) {
              proxy.$modal.msgSuccess("新增成功");
            }
            open.value = false;
            getList();
          });
        }
      }
    });
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const stationIds = row.id || ids.value;
  const confirmText = '是否确认删除站点名称为"' + (row.name || stationIds) + '"的数据项？';

  if (proxy.$modal) {
    proxy.$modal.confirm(confirmText).then(function() {
      return delMonitorStation(stationIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }
}

/** 分页处理 */
function handlePagination(pagination) {
  queryParams.value.pageNum = pagination.page;
  queryParams.value.pageSize = pagination.limit;
  getList();
}

/** 导出按钮操作 */
function handleExport() {
  if (proxy.download) {
    proxy.download('detection/station/export', {
      ...queryParams.value
    }, `monitor_station_${new Date().getTime()}.xlsx`);
  }
}

/** 监测因子按钮操作 */
function handleMonitorFactor(row) {
  currentSystemCode.value = row.systemCode;
  factorQueryParams.value.monitoringSystemSystemCode = row.systemCode;
  factorDialogVisible.value = true;
  getFactorList();
}

/** 查询监测因子列表 */
function getFactorList() {
  factorLoading.value = true;
  listMonitorFactor(factorQueryParams.value).then(response => {
    factorList.value = response.rows;
    factorTotal.value = response.total;
    factorLoading.value = false;
  }).catch(() => {
    factorLoading.value = false;
  });
}

/** 监测因子选择变化 */
function handleFactorSelectionChange(selection) {
  factorIds.value = selection.map(item => item.id);
  factorMultiple.value = selection.length === 0;
}

/** 新增监测因子 */
function handleAddFactor() {
  resetFactorForm();
  factorFormVisible.value = true;
  factorTitle.value = "添加监测因子";
}

/** 修改监测因子 */
function handleUpdateFactor(row) {
  resetFactorForm();
  const id = row.id;
  getMonitorFactor(id).then(response => {
    factorForm.value = response.data;
    factorFormVisible.value = true;
    factorTitle.value = "修改监测因子";
  });
}

/** 删除监测因子 */
function handleDeleteFactor(row) {
  const factorIds = row.id || factorIds.value;
  const confirmText = '是否确认删除选中的监测因子？';

  if (proxy.$modal) {
    proxy.$modal.confirm(confirmText).then(function() {
      return delMonitorFactor(factorIds);
    }).then(() => {
      getFactorList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }
}

/** 重置监测因子表单 */
function resetFactorForm() {
  factorForm.value = {
    id: null,
    code: null,
    name: null,
    minValue: null,
    maxValue: null,
    unit: null,
    sensorId: null,
    factorIndex: 0,
    exceeded: 0,
    faulty: 0,
    monitoringSystemSystemCode: currentSystemCode.value
  };
  if (proxy.resetForm) {
    proxy.resetForm("factorFormRef");
  }
}

/** 提交监测因子表单 */
function submitFactorForm() {
  if (proxy.$refs && proxy.$refs["factorFormRef"]) {
    proxy.$refs["factorFormRef"].validate(valid => {
      if (valid) {
        // 确保设置系统编码
        factorForm.value.monitoringSystemSystemCode = currentSystemCode.value;
        
        if (factorForm.value.id != null) {
          updateMonitorFactor(factorForm.value).then(response => {
            if (proxy.$modal) {
              proxy.$modal.msgSuccess("修改成功");
            }
            factorFormVisible.value = false;
            getFactorList();
          });
        } else {
          addMonitorFactor(factorForm.value).then(response => {
            if (proxy.$modal) {
              proxy.$modal.msgSuccess("新增成功");
            }
            factorFormVisible.value = false;
            getFactorList();
          });
        }
      }
    });
  }
}

/** 取消监测因子表单 */
function cancelFactor() {
  factorFormVisible.value = false;
  resetFactorForm();
}



onMounted(() => {
  // 初始化省份数据
  provinces.value = getProvinces();
  getList();
});
</script>

<style scoped>
.road-status-tabs {
  transition: all 0.3s ease;
}

.road-status-tabs:hover {
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);
}

.tabs-container .el-button {
  transition: all 0.2s ease;
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
}

.tabs-container .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tabs-container .el-button--primary {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
  font-weight: 500;
}

.tabs-container .el-button--default {
  background-color: #fff;
  border-color: #dcdfe6;
  color: #606266;
}

.tabs-container .el-button--default:hover {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tabs-container {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .tabs-container > span {
    margin-bottom: 8px;
  }
}
</style>