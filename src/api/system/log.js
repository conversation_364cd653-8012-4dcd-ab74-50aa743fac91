import request from '@/utils/request'

// 查询图片压缩包处理日志列表
export function listLog(query) {
  return request({
    url: '/project/log/list',
    method: 'post',
    data: query
  })
}

// 查询图片压缩包处理日志详细
export function getLog(id) {
  return request({
    url: '/project/log/' + id,
    method: 'get'
  })
}

// 新增图片压缩包处理日志
export function addLog(data) {
  return request({
    url: '/project/log',
    method: 'post',
    data: data
  })
}

// 修改图片压缩包处理日志
export function updateLog(data) {
  return request({
    url: '/project/log',
    method: 'put',
    data: data
  })
}

// 删除图片压缩包处理日志
export function delLog(id) {
  return request({
    url: '/project/log/' + id,
    method: 'delete'
  })
}
