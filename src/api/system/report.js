import request from '@/utils/request'

// 查询项目隧道检测报告管理列表
export function listReport(query) {
  return request({
    url: '/tunnel/report/list?pageSize='+query.pageSize+'&pageNum='+query.pageNum,
    method: 'post',
    data: query
  })
}

// 查询项目隧道检测报告管理详细
export function getReport(id) {
  return request({
    url: '/tunnel/report/' + id,
    method: 'get'
  })
}

// 新增项目隧道检测报告管理
export function addReport(data) {
  return request({
    url: '/tunnel/report',
    method: 'post',
    data: data
  })
}

// 修改项目隧道检测报告管理
export function updateReport(data) {
  return request({
    url: '/tunnel/report',
    method: 'put',
    data: data
  })
}

// 删除项目隧道检测报告管理
export function delReport(id) {
  return request({
    url: '/tunnel/report/' + id,
    method: 'delete'
  })
}
export function create(id) {
  return request({
    url: '/tunnel/report/generaterReport?id=' + id,
    method: 'post'
  })
}

export function getProjects() {
  return request({
    url: '/project/project/searchAlllist',
    method: 'post',
      data: {}
  })
}

export function getTunnels() {
  return request({
    url: '/tunnel/manage/searchAllList',
    method: 'post',
      data: {}
  })
}

export function exportData(data) {
  return request({
    url: '/tunnel/report/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function getDetailReport(id) {
  return request({
    url: '/tunnel/score/getScoreDetail?id=' + id,
    method: 'post'
  })
}

export function getDetailScoreValue(id) {
    return request({
        url: '/tunnel/detail/getDetailScoreValue?id=' + id,
        method: 'post'
    })
}
