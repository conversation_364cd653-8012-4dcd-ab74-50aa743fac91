import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询工程列表
export function queryIndexDTO(query) {
  return request({
    url: '/tunnel/index/queryIndexDTO',
    method: 'get',
    params: query
  })
}


// 修改工程
export function updateIndexDTO(data) {
  return request({
    url: '/tunnel/index/updateIndexDTO',
    method: 'post',
    data: data
  })
}

//查询所有隧道
export function getTunnelList() {
    return request({
      url: '/tunnel/manage/searchAllList',
      method: 'post',
      data: {}
    })
}

