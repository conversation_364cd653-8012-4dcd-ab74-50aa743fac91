import request from '@/utils/request'

// 查询基础项目路线信息列表
export function listRoad(query) {
  return request({
    url: '/report/road/list',
    method: 'get',
    params: query
  })
}

export function listAllRoad(query) {
  return request({
    url: '/report/road/listAllRoad',
    method: 'get',
    params: query
  })
}

// 查询基础项目路线信息详细
export function getRoad(id) {
  return request({
    url: '/report/road/' + id,
    method: 'get'
  })
}

// 新增基础项目路线信息
export function addRoad(data) {
  return request({
    url: '/report/road',
    method: 'post',
    data: data
  })
}

// 修改基础项目路线信息
export function updateRoad(data) {
  return request({
    url: '/report/road',
    method: 'put',
    data: data
  })
}

// 删除基础项目路线信息
export function delRoad(id) {
  return request({
    url: '/report/road/' + id,
    method: 'delete'
  })
}

// 导出基础项目路线信息
export function exportRoad(query) {
  return request({
    url: '/report/road/export',
    method: 'post',
    params: query,
    responseType: 'blob'
  })
}