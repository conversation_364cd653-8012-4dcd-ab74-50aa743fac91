import request from '@/utils/request'

export function listCheckBridge(query) {
  return request({
    url: '/report/checkBridge/list',
    method: 'get',
    params: query
  })
}

export function getCheckBridge(id) {
  return request({
    url: '/report/checkBridge/' + id,
    method: 'get'
  })
}

export function addCheckBridge(data) {
  return request({
    url: '/report/checkBridge',
    method: 'post',
    data: data
  })
}

export function updateCheckBridge(data) {
  return request({
    url: '/report/checkBridge',
    method: 'put',
    data: data
  })
}

export function delCheckBridge(id) {
  return request({
    url: '/report/checkBridge/' + id,
    method: 'delete'
  })
}

export function exportCheckBridge(query) {
  return request({
    url: '/report/checkBridge/export',
    method: 'post',
    data: query
  })
}

export function countCheckBridge(query) {
  return request({
    url: '/report/checkBridge/count',
    method: 'get',
    params: query
  })
}

export function auditCheckBridge(data) {
  return request({
    url: '/report/checkBridge/audit',
    method: 'post',
    data: data
  })
} 