import request from '@/utils/request'

// 查询监测因子列表
export function listMonitorFactor(query) {
  return request({
    url: '/detection/factor/list',
    method: 'post',
    data: query
  })
}

// 查询监测因子详细
export function getMonitorFactor(id) {
  return request({
    url: '/detection/factor/' + id,
    method: 'get'
  })
}

// 新增监测因子
export function addMonitorFactor(data) {
  return request({
    url: '/detection/factor',
    method: 'post',
    data: data
  })
}

// 修改监测因子
export function updateMonitorFactor(data) {
  return request({
    url: '/detection/factor',
    method: 'put',
    data: data
  })
}

// 删除监测因子
export function delMonitorFactor(id) {
  return request({
    url: '/detection/factor/' + id,
    method: 'delete'
  })
}

// 导出监测因子
export function exportMonitorFactor(query) {
  return request({
    url: '/detection/factor/export',
    method: 'post',
    data: query
  })
}
