import request from '@/utils/request'

// 查询监测站点列表
export function listMonitorStation(query) {
  return request({
    url: '/detection/station/selectByPage',
    method: 'post',
    data: query
  })
}

// 查询监测站点详细
export function getMonitorStation(id) {
  return request({
    url: '/detection/station/' + id,
    method: 'get'
  })
}

// 新增监测站点
export function addMonitorStation(data) {
  return request({
    url: '/detection/station',
    method: 'post',
    data: data
  })
}

// 修改监测站点
export function updateMonitorStation(data) {
  return request({
    url: '/detection/station',
    method: 'put',
    data: data
  })
}

// 删除监测站点
export function delMonitorStation(id) {
  return request({
    url: '/detection/station/' + id,
    method: 'delete'
  })
}

// 导出监测站点
export function exportMonitorStation(query) {
  return request({
    url: '/detection/station/export',
    method: 'post',
    data: query
  })
}
