import request from '@/utils/request'

// 查询路面车辙深度检测列表
export function listRoadCheckRd(query) {
  return request({
    url: '/road/check/rd/list',
    method: 'get',
    params: query
  })
}

// 查询路面车辙深度检测详细
export function getRoadCheckRd(id) {
  return request({
    url: '/road/check/rd/' + id,
    method: 'get'
  })
}

// 新增路面车辙深度检测
export function addRoadCheckRd(data) {
  return request({
    url: '/road/check/rd',
    method: 'post',
    data: data
  })
}

// 修改路面车辙深度检测
export function updateRoadCheckRd(data) {
  return request({
    url: '/road/check/rd',
    method: 'put',
    data: data
  })
}

// 删除路面车辙深度检测
export function delRoadCheckRd(id) {
  return request({
    url: '/road/check/rd/' + id,
    method: 'delete'
  })
}

// 根据道路ID获取路面车辙深度检测列表
export function listRoadCheckRdByRoadId(roadId) {
  return request({
    url: '/road/check/rd/listByRoadId/' + roadId,
    method: 'get'
  })
}

// 获取路面车辙深度检测记录数
export function countRoadCheckRd(query) {
  return request({
    url: '/road/check/rd/count',
    method: 'get',
    params: query
  })
} 