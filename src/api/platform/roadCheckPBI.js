import request from '@/utils/request'

// 查询路面跳车检测列表
export function listRoadCheckBump(query) {
  return request({
    url: '/road/check/pbi/list',
    method: 'get',
    params: query
  })
}

// 查询路面跳车检测详细
export function getRoadCheckBump(id) {
  return request({
    url: '/road/check/pbi/' + id,
    method: 'get'
  })
}

// 新增路面跳车检测
export function addRoadCheckBump(data) {
  return request({
    url: '/road/check/pbi',
    method: 'post',
    data: data
  })
}

// 修改路面跳车检测
export function updateRoadCheckBump(data) {
  return request({
    url: '/road/check/pbi',
    method: 'put',
    data: data
  })
}

// 删除路面跳车检测
export function delRoadCheckBump(id) {
  return request({
    url: '/road/check/pbi/' + id,
    method: 'delete'
  })
}

// 根据道路ID获取路面跳车检测列表
export function listRoadCheckBumpByRoadId(roadId) {
  return request({
    url: '/road/check/pbi/listByRoadId/' + roadId,
    method: 'get'
  })
}

// 获取路面跳车检测记录数
export function countRoadCheckBump(query) {
  return request({
    url: '/road/check/pbi/count',
    method: 'get',
    params: query
  })
} 