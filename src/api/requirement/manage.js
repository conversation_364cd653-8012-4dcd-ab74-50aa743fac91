import request from '@/utils/request'

let headers = {
  'isToken': true
}

// 查询设备数据信息列表
export function listRequirementData(query) {
  return request({
    url: '/requirement/manage/list',
    method: 'post',
    params: {
      "pageNum": query.pageNum,
      "pageSize": query.pageSize
    },
    data: query,
    headers: headers,
  })
}

// 查询设备数据信息详细
export function getRequirementData(id) {
  return request({
    url: '/requirement/manage/' + id,
    method: 'get',
    headers: headers,
  })
}

// 新增设备数据信息
export function addRequirementData(data) {
  return request({
    url: '/requirement/manage',
    method: 'post',
    data: data,
    headers: headers,
  })
}

// 修改设备数据信息
export function updateRequirementData(data) {
  return request({
    url: '/requirement/manage',
    method: 'put',
    data: data,
    headers: headers,
  })
}

// 删除设备数据信息
export function delRequirementData(key) {
  return request({
    url: '/requirement/manage/' + key,
    method: 'delete',
    headers: headers,
  })
}
