import request from '@/utils/request'

let headers = {
  'isToken': true
}

// 查询检测数据信息列表
export function listCheckData(query) {
  return request({
    url: '/check/checkData/list',
    method: 'post',
    params: {
      "pageNum": query.pageNum,
      "pageSize": query.pageSize
    },
    data: query,
    headers: headers,
  })
}

// 查询检测数据信息详细
export function getCheckData(key) {
  return request({
    url: '/check/checkData/' + key,
    method: 'get',
    headers: headers,
  })
}

// 新增检测数据信息
export function addCheckData(data) {
  return request({
    url: '/check/checkData/add',
    method: 'post',
    data: data,
    headers: headers,
  })
}

// 修改检测数据信息
export function updateCheckData(data) {
  return request({
    url: '/check/checkData/edit',
    method: 'post',
    data: data,
    headers: headers,
  })
}

// 删除检测数据信息
export function delCheckData(key) {
  return request({
    url: '/check/checkData/remove',
    method: 'post',
    params: {
      keys: key
    },
    headers: headers,
  })
}

// 导出检测数据信息
export function exportCheckData(query) {
  return request({
    url: '/check/checkData/export',
    method: 'post',
    data: query,
    responseType: 'blob',
    headers: headers,
  })
}

// 获取检测数据属性映射
export function getPropertyMapping() {
  return request({
    url: '/check/checkData/property/mapping',
    method: 'get',
    headers: headers,
  })
}
