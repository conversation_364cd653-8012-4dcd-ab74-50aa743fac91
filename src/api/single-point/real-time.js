import request from '@/utils/request';
// 监测类型
export const selectMonitorStationByType = (data) => {
    return request({
        url: '/detection/single/selectMonitorStationByType',
        method: 'post',
        data
    })
}
// 有效日期
// http://47.103.13.128:8109/detection/single/selectTimeRange
export const selectTimeRange = (data) => {
    return request({
        url: '/detection/single/selectTimeRange',
        method: 'post',
        data
    })
}
// 列表动态回显
// http://47.103.13.128:8109/detection/single/getFieldsByType
export const getFieldsByType = (data) => {
    return request({
        url: '/detection/single/getFieldsByType',
        method: 'post',
        data
    })
}
// http://47.103.13.128:8109/detection/single/selectMonitorListByPage
// 分页
export const selectMonitorListByPage = (data) => {
    return request({
        url: '/detection/single/selectMonitorListByPage',
        method: 'post',
        data
    })
}
// echarts 
// http://47.103.13.128:8109/detection/single/selectMonitorListForChart
export const selectMonitorListForChart = (data) => {
    return request({
        url: '/detection/single/selectMonitorListForChart',
        method: 'post',
        data
    })
}

// http://127.0.0.1:8109/detection/single/selectMonitorListByPage