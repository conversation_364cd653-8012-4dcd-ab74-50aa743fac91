
// 格式化日期为 YYYY-MM-DD HH:mm:ss
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
// 获取当天开始时间并格式化
export function getStartOfDayFormatted() {
    const date = new Date();
    date.setHours(0, 0, 0, 0);
    return formatDate(date);
}

// 获取当前时间并格式化
export function getCurrentTimeFormatted() {
    return formatDate(new Date());
}

export function timeRange() {
    return {
        startTime: getStartOfDayFormatted(),
        endTime: getCurrentTimeFormatted()
    }
}



// // 获取时间段
// const timeRange = {
//   start: getStartOfDayFormatted(),
//   end: getCurrentTimeFormatted()
// };

// console.log('今天开始时间:', timeRange.start);
// console.log('当前时间:', timeRange.end);